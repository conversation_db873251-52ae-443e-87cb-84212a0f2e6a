import {
  require_zig
} from "./chunk-7M2POC2H.js";
import {
  require_core
} from "./chunk-QQZNFZZN.js";
import {
  require_wiki
} from "./chunk-D2GUJUSL.js";
import {
  require_wolfram
} from "./chunk-P2ZOVZWS.js";
import {
  require_wren
} from "./chunk-QPRJ22VM.js";
import {
  require_xeora
} from "./chunk-PTHMREPV.js";
import {
  require_xml_doc
} from "./chunk-VAZLN6H4.js";
import {
  require_xojo
} from "./chunk-HIBETL64.js";
import {
  require_xquery
} from "./chunk-Q724GKYV.js";
import {
  require_yang
} from "./chunk-2EBGQIUO.js";
import {
  require_velocity
} from "./chunk-2ENMNLCY.js";
import {
  require_verilog
} from "./chunk-OX6O74LW.js";
import {
  require_vhdl
} from "./chunk-MTPBLBU6.js";
import {
  require_vim
} from "./chunk-UBTJTKUE.js";
import {
  require_visual_basic
} from "./chunk-ZT5SJL2X.js";
import {
  require_warpscript
} from "./chunk-5QMZSI4Z.js";
import {
  require_wasm
} from "./chunk-IPIXV246.js";
import {
  require_web_idl
} from "./chunk-ATNGBJST.js";
import {
  require_tt2
} from "./chunk-6MMZQ3ZV.js";
import {
  require_twig
} from "./chunk-T5G7FP5G.js";
import {
  require_typoscript
} from "./chunk-3S75K4I5.js";
import {
  require_unrealscript
} from "./chunk-UXJBW6FO.js";
import {
  require_uorazor
} from "./chunk-G6Y2MA4O.js";
import {
  require_uri
} from "./chunk-FLQ3QCFS.js";
import {
  require_v
} from "./chunk-6KHA3BPN.js";
import {
  require_vala
} from "./chunk-UR53UCIE.js";
import {
  require_t4_vb
} from "./chunk-5DMG6FFV.js";
import {
  require_tap
} from "./chunk-X3UH2EQT.js";
import {
  require_yaml
} from "./chunk-ITXP5SW6.js";
import {
  require_tcl
} from "./chunk-KTGYU5MG.js";
import {
  require_textile
} from "./chunk-PRSAFTR5.js";
import {
  require_toml
} from "./chunk-BDPLHYK6.js";
import {
  require_tremor
} from "./chunk-YBOIU7HH.js";
import {
  require_tsx
} from "./chunk-JNWJ465R.js";
import {
  require_squirrel
} from "./chunk-LFJM2OUE.js";
import {
  require_stan
} from "./chunk-KLNEUFGH.js";
import {
  require_stylus
} from "./chunk-NWCYQRGI.js";
import {
  require_swift
} from "./chunk-KQXCSCQO.js";
import {
  require_systemd
} from "./chunk-25VB6OPP.js";
import {
  require_t4_cs
} from "./chunk-7H4WW3BX.js";
import {
  require_t4_templating
} from "./chunk-WMQPI3TA.js";
import {
  require_vbnet
} from "./chunk-NRNNA3WD.js";
import {
  require_sml
} from "./chunk-QCM557KY.js";
import {
  require_solidity
} from "./chunk-6V7IHYTW.js";
import {
  require_solution_file
} from "./chunk-VOEN5XDC.js";
import {
  require_soy
} from "./chunk-MLWQTBII.js";
import {
  require_sparql
} from "./chunk-QAGPCHMW.js";
import {
  require_turtle
} from "./chunk-HSNEQKRR.js";
import {
  require_splunk_spl
} from "./chunk-OSFULHVD.js";
import {
  require_sqf
} from "./chunk-H3ICK2B7.js";
import {
  require_sas
} from "./chunk-P55JVUUS.js";
import {
  require_sass
} from "./chunk-Q7BKAAKD.js";
import {
  require_scala
} from "./chunk-NS6ECDZB.js";
import {
  require_scss
} from "./chunk-U6XGBLEP.js";
import {
  require_shell_session
} from "./chunk-MHXQS3YU.js";
import {
  require_smali
} from "./chunk-O7OTDWH4.js";
import {
  require_smalltalk
} from "./chunk-I6SRSJS6.js";
import {
  require_smarty
} from "./chunk-S3IFUSY2.js";
import {
  require_regex
} from "./chunk-V6O7CDAJ.js";
import {
  require_rego
} from "./chunk-5TZMUUTU.js";
import {
  require_renpy
} from "./chunk-2P7BKAYX.js";
import {
  require_rest
} from "./chunk-UG4J7UZV.js";
import {
  require_rip
} from "./chunk-4H7A7LVM.js";
import {
  require_roboconf
} from "./chunk-SKNVLG4K.js";
import {
  require_robotframework
} from "./chunk-4LYTTRAQ.js";
import {
  require_rust
} from "./chunk-GPLC2RKT.js";
import {
  require_python
} from "./chunk-OGBLZL7A.js";
import {
  require_q
} from "./chunk-EHBNPB74.js";
import {
  require_qml
} from "./chunk-VDUHNDTH.js";
import {
  require_qore
} from "./chunk-B46SE3BK.js";
import {
  require_qsharp
} from "./chunk-HXYYGBEI.js";
import {
  require_r
} from "./chunk-MMG376IV.js";
import {
  require_racket
} from "./chunk-2J6OGAE2.js";
import {
  require_reason
} from "./chunk-ZJGGJH27.js";
import {
  require_properties
} from "./chunk-F5HVQML4.js";
import {
  require_protobuf
} from "./chunk-CSF6LVX7.js";
import {
  require_psl
} from "./chunk-WHT2VJNH.js";
import {
  require_pug
} from "./chunk-GCBAHTYA.js";
import {
  require_puppet
} from "./chunk-ZWOGP72Q.js";
import {
  require_pure
} from "./chunk-5XZV5QDQ.js";
import {
  require_purebasic
} from "./chunk-AXWXEMN2.js";
import {
  require_purescript
} from "./chunk-ZBRJMXSG.js";
import {
  require_php_extras
} from "./chunk-BJUZ2TNS.js";
import {
  require_phpdoc
} from "./chunk-FYCEBRZI.js";
import {
  require_plsql
} from "./chunk-HEDGWAK3.js";
import {
  require_powerquery
} from "./chunk-JXWXTI3Q.js";
import {
  require_powershell
} from "./chunk-QUBTWNSP.js";
import {
  require_processing
} from "./chunk-IY74U3V5.js";
import {
  require_prolog
} from "./chunk-GOZSA4FU.js";
import {
  require_promql
} from "./chunk-CRRDW65L.js";
import {
  require_oz
} from "./chunk-CWNMNT3A.js";
import {
  require_parigp
} from "./chunk-RBEZZZTM.js";
import {
  require_parser
} from "./chunk-TEXSHN6O.js";
import {
  require_pascal
} from "./chunk-HTNSVNVA.js";
import {
  require_pascaligo
} from "./chunk-D3JZYPFY.js";
import {
  require_pcaxis
} from "./chunk-DH52N67G.js";
import {
  require_peoplecode
} from "./chunk-CT6TU4ZG.js";
import {
  require_perl
} from "./chunk-2X2EQ27M.js";
import {
  require_nginx
} from "./chunk-GXDNYW67.js";
import {
  require_nim
} from "./chunk-FDITBBNI.js";
import {
  require_nix
} from "./chunk-5TKPOHCT.js";
import {
  require_nsis
} from "./chunk-P6RQJHNW.js";
import {
  require_objectivec
} from "./chunk-XBDS4SNO.js";
import {
  require_ocaml
} from "./chunk-CORL24C2.js";
import {
  require_opencl
} from "./chunk-FEUCQ6KP.js";
import {
  require_openqasm
} from "./chunk-CAZMH2EG.js";
import {
  require_moonscript
} from "./chunk-E66PN4BN.js";
import {
  require_n1ql
} from "./chunk-WZGQ4YUH.js";
import {
  require_n4js
} from "./chunk-CCOMK2YB.js";
import {
  require_nand2tetris_hdl
} from "./chunk-P2OOMCQX.js";
import {
  require_naniscript
} from "./chunk-AGX77QTH.js";
import {
  require_nasm
} from "./chunk-RB7NE22J.js";
import {
  require_neon
} from "./chunk-QR64Q43I.js";
import {
  require_nevod
} from "./chunk-L3PHBY2F.js";
import {
  require_matlab
} from "./chunk-FBVWPB3M.js";
import {
  require_maxscript
} from "./chunk-I2XUVVN2.js";
import {
  require_mel
} from "./chunk-RWUKL6B2.js";
import {
  require_mermaid
} from "./chunk-DKUYSL5J.js";
import {
  require_mizar
} from "./chunk-DG2NGMSH.js";
import {
  require_mongodb
} from "./chunk-KPLYTGWL.js";
import {
  require_monkey
} from "./chunk-PSBCBMEB.js";
import {
  require_lisp
} from "./chunk-NSCBATTI.js";
import {
  require_livescript
} from "./chunk-KF4AIWR6.js";
import {
  require_llvm
} from "./chunk-44MO2VMC.js";
import {
  require_log
} from "./chunk-HF6A5GBU.js";
import {
  require_lolcode
} from "./chunk-D4D32SYQ.js";
import {
  require_magma
} from "./chunk-YDOBFR2L.js";
import {
  require_makefile
} from "./chunk-BHQR7AV6.js";
import {
  require_markdown
} from "./chunk-WCGVWRS5.js";
import {
  require_kusto
} from "./chunk-VYZSXXCA.js";
import {
  require_latex
} from "./chunk-FVLEIWUW.js";
import {
  require_latte
} from "./chunk-JK6IRROH.js";
import {
  require_php
} from "./chunk-ZRTO2JHE.js";
import {
  require_less
} from "./chunk-UDQVJWPV.js";
import {
  require_lilypond
} from "./chunk-RM4Y6D5J.js";
import {
  require_scheme
} from "./chunk-AE6OWAGP.js";
import {
  require_liquid
} from "./chunk-YJVWXWJW.js";
import {
  require_jsonp
} from "./chunk-NR7MJNPV.js";
import {
  require_jsstacktrace
} from "./chunk-M65ZMLVX.js";
import {
  require_jsx
} from "./chunk-2HGURQO7.js";
import {
  require_julia
} from "./chunk-AJMXONJ2.js";
import {
  require_keepalived
} from "./chunk-VQG3VUO2.js";
import {
  require_keyman
} from "./chunk-CGGJPBP2.js";
import {
  require_kotlin
} from "./chunk-YTEK42GS.js";
import {
  require_kumir
} from "./chunk-NBW4BFZV.js";
import {
  require_jolie
} from "./chunk-655P4A2A.js";
import {
  require_jq
} from "./chunk-7ZMXTXM3.js";
import {
  require_js_extras
} from "./chunk-RT3O24D5.js";
import {
  require_js_templates
} from "./chunk-5O5KU7MF.js";
import {
  require_jsdoc
} from "./chunk-PNY66S4V.js";
import {
  require_typescript
} from "./chunk-GACOEHED.js";
import {
  require_json5
} from "./chunk-4WGY3TIU.js";
import {
  require_json
} from "./chunk-LHOFSBPR.js";
import {
  require_io
} from "./chunk-TADM6EYG.js";
import {
  require_j
} from "./chunk-OWXPWV53.js";
import {
  require_javadoc
} from "./chunk-VVU3L6MU.js";
import {
  require_java
} from "./chunk-M2KQQDHD.js";
import {
  require_javadoclike
} from "./chunk-VT3P3RMA.js";
import {
  require_javastacktrace
} from "./chunk-QDHBRX55.js";
import {
  require_jexl
} from "./chunk-O4ATK7QO.js";
import {
  require_ichigojam
} from "./chunk-H75M2DVQ.js";
import {
  require_icon
} from "./chunk-6WJSIF3R.js";
import {
  require_icu_message_format
} from "./chunk-5SKOWBK5.js";
import {
  require_idris
} from "./chunk-BMPSMPIR.js";
import {
  require_iecst
} from "./chunk-DAIQIKGB.js";
import {
  require_ignore
} from "./chunk-ZZEMPO7K.js";
import {
  require_inform7
} from "./chunk-T3XWRKAV.js";
import {
  require_ini
} from "./chunk-2S5GCJNP.js";
import {
  require_haskell
} from "./chunk-LNOVDPFW.js";
import {
  require_haxe
} from "./chunk-6QL2F5O2.js";
import {
  require_hcl
} from "./chunk-VXFBYRGV.js";
import {
  require_hlsl
} from "./chunk-UIDAWY4P.js";
import {
  require_hoon
} from "./chunk-KHK6XGVB.js";
import {
  require_hpkp
} from "./chunk-2F56KTSH.js";
import {
  require_hsts
} from "./chunk-D5I7LFZM.js";
import {
  require_http
} from "./chunk-YGEMHGDH.js";
import {
  require_gml
} from "./chunk-QXZ6LZEE.js";
import {
  require_gn
} from "./chunk-27FQLOFD.js";
import {
  require_go_module
} from "./chunk-IXM6FZXN.js";
import {
  require_go
} from "./chunk-ZSO3MDLK.js";
import {
  require_graphql
} from "./chunk-BAVFZIIC.js";
import {
  require_groovy
} from "./chunk-HBNGKEJH.js";
import {
  require_haml
} from "./chunk-YFHZEIYR.js";
import {
  require_handlebars
} from "./chunk-T7H4BFUN.js";
import {
  require_ftl
} from "./chunk-WLBLYNMU.js";
import {
  require_gap
} from "./chunk-JBK444AG.js";
import {
  require_gcode
} from "./chunk-TBM45AVT.js";
import {
  require_gdscript
} from "./chunk-GUV5BCAB.js";
import {
  require_gedcom
} from "./chunk-NKUQGGDK.js";
import {
  require_gherkin
} from "./chunk-QX47YLXA.js";
import {
  require_git
} from "./chunk-4XF7WOAR.js";
import {
  require_glsl
} from "./chunk-EJ6QKDAB.js";
import {
  require_etlua
} from "./chunk-UYSLPTKY.js";
import {
  require_excel_formula
} from "./chunk-E5HIOCAI.js";
import {
  require_factor
} from "./chunk-NILDFLZ2.js";
import {
  require_false
} from "./chunk-XEP4OYNO.js";
import {
  require_firestore_security_rules
} from "./chunk-7VBCW222.js";
import {
  require_flow
} from "./chunk-P3CUK5CR.js";
import {
  require_fortran
} from "./chunk-26RXCLCF.js";
import {
  require_fsharp
} from "./chunk-AQLJ2FCZ.js";
import {
  require_editorconfig
} from "./chunk-2WXXMIFN.js";
import {
  require_eiffel
} from "./chunk-UA4B3RPS.js";
import {
  require_ejs
} from "./chunk-PED2KI5K.js";
import {
  require_elixir
} from "./chunk-QVWBAQP3.js";
import {
  require_elm
} from "./chunk-42HIRB4H.js";
import {
  require_erb
} from "./chunk-Z7FLJEJU.js";
import {
  require_erlang
} from "./chunk-5GAYO7BI.js";
import {
  require_lua
} from "./chunk-VAXSCFA5.js";
import {
  require_dhall
} from "./chunk-OTJJCZM5.js";
import {
  require_diff
} from "./chunk-XUKLDMSM.js";
import {
  require_django
} from "./chunk-EZX3XVLO.js";
import {
  require_markup_templating
} from "./chunk-VZF54O3Z.js";
import {
  require_dns_zone_file
} from "./chunk-XBTKYJ4R.js";
import {
  require_docker
} from "./chunk-T7CUMVP4.js";
import {
  require_dot
} from "./chunk-NUC4OBKZ.js";
import {
  require_ebnf
} from "./chunk-ALOO5JY7.js";
import {
  require_css_extras
} from "./chunk-4SC7SCE4.js";
import {
  require_csv
} from "./chunk-O3SCOLDO.js";
import {
  require_cypher
} from "./chunk-NXQEDGDR.js";
import {
  require_d
} from "./chunk-64Y26XWY.js";
import {
  require_dart
} from "./chunk-72ZZJZDB.js";
import {
  require_dataweave
} from "./chunk-M46XOCMW.js";
import {
  require_dax
} from "./chunk-RAHTUDSQ.js";
import {
  require_cobol
} from "./chunk-IRPAWSKW.js";
import {
  require_coffeescript
} from "./chunk-6XFMLMTS.js";
import {
  require_concurnas
} from "./chunk-BU6S4RUV.js";
import {
  require_coq
} from "./chunk-BZ6ET4PA.js";
import {
  require_crystal
} from "./chunk-3JOUJV3X.js";
import {
  require_ruby
} from "./chunk-N7YZMM4D.js";
import {
  require_cshtml
} from "./chunk-D6BTMIGY.js";
import {
  require_csp
} from "./chunk-S2DS7HJJ.js";
import {
  require_bro
} from "./chunk-C326ZXKI.js";
import {
  require_bsl
} from "./chunk-EGHHVUWV.js";
import {
  require_cfscript
} from "./chunk-TYQLZA4U.js";
import {
  require_chaiscript
} from "./chunk-II7F2QNM.js";
import {
  require_cil
} from "./chunk-T3YWEUNT.js";
import {
  require_clojure
} from "./chunk-WB4TRG2M.js";
import {
  require_cmake
} from "./chunk-O3T4SRVA.js";
import {
  require_batch
} from "./chunk-Y6MJNE5N.js";
import {
  require_bbcode
} from "./chunk-RHYCMTGX.js";
import {
  require_bicep
} from "./chunk-CLMZS5B7.js";
import {
  require_birb
} from "./chunk-CVOJUTIJ.js";
import {
  require_bison
} from "./chunk-BXXF7UYB.js";
import {
  require_bnf
} from "./chunk-OA3BDJGT.js";
import {
  require_brainfuck
} from "./chunk-M3KY2GR2.js";
import {
  require_brightscript
} from "./chunk-ONQIFMQR.js";
import {
  require_aspnet
} from "./chunk-PU5TATJG.js";
import {
  require_csharp
} from "./chunk-ZEA4FJSR.js";
import {
  require_autohotkey
} from "./chunk-U7VHXHCG.js";
import {
  require_autoit
} from "./chunk-JTF7AKKU.js";
import {
  require_avisynth
} from "./chunk-E7WE4SSF.js";
import {
  require_avro_idl
} from "./chunk-AHU5DFCK.js";
import {
  require_bash
} from "./chunk-3KUEDU6R.js";
import {
  require_basic
} from "./chunk-TYZNI2Y3.js";
import {
  require_aql
} from "./chunk-AEDV32HP.js";
import {
  require_arduino
} from "./chunk-NA5K3F5U.js";
import {
  require_cpp
} from "./chunk-UFXCSKGJ.js";
import {
  require_c
} from "./chunk-HWHRKZWV.js";
import {
  require_arff
} from "./chunk-AXR3ZB2P.js";
import {
  require_asciidoc
} from "./chunk-KZI4HZJU.js";
import {
  require_asm6502
} from "./chunk-AYIONBXL.js";
import {
  require_asmatmel
} from "./chunk-DENOJKSV.js";
import {
  require_agda
} from "./chunk-TNYZYNKN.js";
import {
  require_al
} from "./chunk-OJSUBNKP.js";
import {
  require_antlr4
} from "./chunk-Y7NL6RAK.js";
import {
  require_apacheconf
} from "./chunk-X3P476VC.js";
import {
  require_apex
} from "./chunk-FI5YKTNY.js";
import {
  require_sql
} from "./chunk-UVBXIARX.js";
import {
  require_apl
} from "./chunk-WHSYX5P6.js";
import {
  require_applescript
} from "./chunk-F3BCJVVK.js";
import {
  require_abap
} from "./chunk-HRRFLE35.js";
import {
  require_abnf
} from "./chunk-UOVV7GAN.js";
import {
  require_actionscript
} from "./chunk-LARDMA6L.js";
import {
  require_ada
} from "./chunk-JFQMTWYJ.js";
import {
  __commonJS
} from "./chunk-4MBMRILA.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-ESKYMHRW.js.map
