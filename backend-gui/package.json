{"name": "wow-backend-gui", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@monaco-editor/react": "^4.7.0", "@tanstack/react-query": "^5.79.0", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@xyflow/react": "^12.6.4", "axios": "^1.6.2", "chart.js": "^4.4.9", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.511.0", "prismjs": "^1.30.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "superjson": "^2.2.2", "zod": "^3.25.41", "zustand": "^5.0.5"}, "devDependencies": {"@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^6.3.5"}}