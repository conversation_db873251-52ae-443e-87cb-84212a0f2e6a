import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { 
  Project, 
  ProjectContext, 
  KnowledgeItem, 
  Agent,
  Insight 
} from '../../types/project.types';
import type { 
  KnowledgeGraph, 
  KnowledgeCollection,
  KnowledgeSource 
} from '../../types/knowledge.types';
import type { 
  IntelligenceContext,
  IntelligentSuggestion,
  LearningEvent 
} from '../../types/intelligence.types';
import type { ModuleName, NavigationState } from '../../types/navigation.types';

interface LighthouseState {
  // Navigation
  navigation: NavigationState;
  setCurrentModule: (module: ModuleName) => void;
  navigateToModule: (module: ModuleName, context?: any) => void;
  
  // Projects
  projects: Project[];
  currentProject: Project | null;
  projectContext: ProjectContext | null;
  setCurrentProject: (projectId: string) => void;
  createProject: (project: Omit<Project, 'id' | 'created' | 'updated'>) => Promise<Project>;
  updateProject: (projectId: string, updates: Partial<Project>) => void;
  
  // Knowledge Management
  knowledgeGraph: KnowledgeGraph | null;
  knowledgeCollections: KnowledgeCollection[];
  knowledgeSources: KnowledgeSource[];
  addKnowledgeItem: (item: Omit<KnowledgeItem, 'id'>) => Promise<KnowledgeItem>;
  updateKnowledgeGraph: (updates: Partial<KnowledgeGraph>) => void;
  createCollection: (collection: Omit<KnowledgeCollection, 'id'>) => Promise<KnowledgeCollection>;
  
  // Intelligence
  intelligenceContext: IntelligenceContext | null;
  suggestions: IntelligentSuggestion[];
  learningEvents: LearningEvent[];
  updateIntelligenceContext: (updates: Partial<IntelligenceContext>) => void;
  addSuggestion: (suggestion: IntelligentSuggestion) => void;
  recordLearning: (event: Omit<LearningEvent, 'id' | 'timestamp'>) => void;
  
  // Agents
  activeAgents: Agent[];
  agentHistory: Agent[];
  deployAgent: (agent: Omit<Agent, 'id' | 'status'>) => Promise<Agent>;
  updateAgentStatus: (agentId: string, status: Agent['status'], results?: any) => void;
  
  // Insights
  insights: Insight[];
  addInsight: (insight: Omit<Insight, 'id' | 'timestamp'>) => void;
  connectInsights: (insightId1: string, insightId2: string) => void;
  
  // Session Management
  sessionId: string;
  initializeSession: () => void;
  clearSession: () => void;
}

export const useLighthouseStore = create<LighthouseState>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          // Initial State
          navigation: {
            currentModule: 'dashboard',
            moduleHistory: ['dashboard'],
            timestamp: Date.now()
          },
          projects: [],
          currentProject: null,
          projectContext: null,
          knowledgeGraph: null,
          knowledgeCollections: [],
          knowledgeSources: [],
          intelligenceContext: null,
          suggestions: [],
          learningEvents: [],
          activeAgents: [],
          agentHistory: [],
          insights: [],
          sessionId: crypto.randomUUID(),
          
          // Navigation Actions
          setCurrentModule: (module) => set((state) => {
            state.navigation.previousModule = state.navigation.currentModule;
            state.navigation.currentModule = module;
            state.navigation.moduleHistory.push(module);
            state.navigation.timestamp = Date.now();
          }),
          
          navigateToModule: (module, context) => set((state) => {
            state.navigation.previousModule = state.navigation.currentModule;
            state.navigation.currentModule = module;
            state.navigation.moduleHistory.push(module);
            state.navigation.timestamp = Date.now();
            // Store navigation context if provided
            if (context && state.intelligenceContext) {
              state.intelligenceContext.currentFocus = context.focus;
            }
          }),
          
          // Project Actions
          setCurrentProject: (projectId) => set((state) => {
            const project = state.projects.find(p => p.id === projectId);
            if (project) {
              state.currentProject = project;
              state.projectContext = {
                project,
                activeKnowledge: [],
                recentInsights: state.insights.filter(i => 
                  i.timestamp > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                ),
                runningAgents: state.activeAgents.filter(a => a.projectId === projectId)
              };
            }
          }),
          
          createProject: async (projectData) => {
            const newProject: Project = {
              ...projectData,
              id: crypto.randomUUID(),
              created: new Date(),
              updated: new Date(),
              intelligence: {
                contextId: crypto.randomUUID(),
                knowledgeGraphId: crypto.randomUUID(),
                learningLevel: 0,
                keyInsights: [],
                activePatterns: [],
                domainExpertise: {
                  primaryDomain: projectData.domain,
                  relatedDomains: [],
                  concepts: [],
                  vocabulary: [],
                  expertiseLevel: 0
                }
              }
            };
            
            set((state) => {
              state.projects.push(newProject);
              state.currentProject = newProject;
            });
            
            return newProject;
          },
          
          updateProject: (projectId, updates) => set((state) => {
            const projectIndex = state.projects.findIndex(p => p.id === projectId);
            if (projectIndex !== -1) {
              state.projects[projectIndex] = {
                ...state.projects[projectIndex],
                ...updates,
                updated: new Date()
              };
              if (state.currentProject?.id === projectId) {
                state.currentProject = state.projects[projectIndex];
              }
            }
          }),
          
          // Knowledge Actions
          addKnowledgeItem: async (itemData) => {
            const newItem: KnowledgeItem = {
              ...itemData,
              id: crypto.randomUUID()
            };
            
            set((state) => {
              if (state.projectContext) {
                state.projectContext.activeKnowledge.push(newItem);
              }
            });
            
            return newItem;
          },
          
          updateKnowledgeGraph: (updates) => set((state) => {
            if (state.knowledgeGraph) {
              state.knowledgeGraph = {
                ...state.knowledgeGraph,
                ...updates,
                metadata: {
                  ...state.knowledgeGraph.metadata,
                  lastUpdated: new Date()
                }
              };
            }
          }),
          
          createCollection: async (collectionData) => {
            const newCollection: KnowledgeCollection = {
              ...collectionData,
              id: crypto.randomUUID()
            };
            
            set((state) => {
              state.knowledgeCollections.push(newCollection);
            });
            
            return newCollection;
          },
          
          // Intelligence Actions
          updateIntelligenceContext: (updates) => set((state) => {
            if (state.intelligenceContext) {
              state.intelligenceContext = {
                ...state.intelligenceContext,
                ...updates
              };
            } else if (state.currentProject) {
              state.intelligenceContext = {
                projectId: state.currentProject.id,
                sessionId: state.sessionId,
                user: 'current-user', // TODO: Get from auth
                recentInteractions: [],
                activeNodes: [],
                contextWindow: {
                  size: 4096,
                  content: [],
                  summary: '',
                  relevanceThreshold: 0.7
                },
                ...updates
              };
            }
          }),
          
          addSuggestion: (suggestion) => set((state) => {
            state.suggestions.unshift(suggestion);
            if (state.suggestions.length > 50) {
              state.suggestions = state.suggestions.slice(0, 50);
            }
          }),
          
          recordLearning: (eventData) => set((state) => {
            const event: LearningEvent = {
              ...eventData,
              id: crypto.randomUUID(),
              timestamp: new Date()
            };
            state.learningEvents.push(event);
            
            // Update project learning level
            if (state.currentProject) {
              const learningProgress = state.learningEvents.length * 0.5;
              state.currentProject.intelligence.learningLevel = 
                Math.min(100, state.currentProject.intelligence.learningLevel + learningProgress);
            }
          }),
          
          // Agent Actions
          deployAgent: async (agentData) => {
            const newAgent: Agent = {
              ...agentData,
              id: crypto.randomUUID(),
              status: 'running'
            };
            
            set((state) => {
              state.activeAgents.push(newAgent);
            });
            
            return newAgent;
          },
          
          updateAgentStatus: (agentId, status, results) => set((state) => {
            const agentIndex = state.activeAgents.findIndex(a => a.id === agentId);
            if (agentIndex !== -1) {
              state.activeAgents[agentIndex].status = status;
              if (results) {
                state.activeAgents[agentIndex].results = results;
              }
              
              if (status === 'completed' || status === 'failed') {
                const agent = state.activeAgents[agentIndex];
                state.agentHistory.push(agent);
                state.activeAgents.splice(agentIndex, 1);
              }
            }
          }),
          
          // Insight Actions
          addInsight: (insightData) => set((state) => {
            const newInsight: Insight = {
              ...insightData,
              id: crypto.randomUUID(),
              timestamp: new Date()
            };
            state.insights.push(newInsight);
            
            if (state.currentProject) {
              state.currentProject.intelligence.keyInsights.push(newInsight);
            }
          }),
          
          connectInsights: (insightId1, insightId2) => set((state) => {
            const insight1 = state.insights.find(i => i.id === insightId1);
            const insight2 = state.insights.find(i => i.id === insightId2);
            
            if (insight1 && insight2) {
              if (!insight1.connections.includes(insightId2)) {
                insight1.connections.push(insightId2);
              }
              if (!insight2.connections.includes(insightId1)) {
                insight2.connections.push(insightId1);
              }
            }
          }),
          
          // Session Actions
          initializeSession: () => set((state) => {
            state.sessionId = crypto.randomUUID();
            state.intelligenceContext = null;
            state.suggestions = [];
            
            // Create default demo project if none exists
            if (state.projects.length === 0) {
              const demoProject: Project = {
                id: 'demo-project-1',
                name: 'AI Research Demo',
                description: 'Demonstration project for exploring Lighthouse capabilities',
                domain: 'Artificial Intelligence',
                status: 'active',
                created: new Date(),
                updated: new Date(),
                intelligence: {
                  contextId: crypto.randomUUID(),
                  knowledgeGraphId: crypto.randomUUID(),
                  learningLevel: 75,
                  keyInsights: [],
                  activePatterns: [],
                  domainExpertise: {
                    primaryDomain: 'Artificial Intelligence',
                    relatedDomains: ['Machine Learning', 'Data Science', 'Computer Science'],
                    concepts: [
                      { id: 'c1', name: 'Machine Learning', confidence: 0.95, category: 'AI', connections: 8 },
                      { id: 'c2', name: 'Neural Networks', confidence: 0.88, category: 'AI', connections: 12 },
                      { id: 'c3', name: 'Deep Learning', confidence: 0.91, category: 'AI', connections: 15 },
                      { id: 'c4', name: 'Natural Language Processing', confidence: 0.83, category: 'AI', connections: 6 },
                      { id: 'c5', name: 'Computer Vision', confidence: 0.76, category: 'AI', connections: 4 }
                    ],
                    vocabulary: [
                      { term: 'transformer', frequency: 45, relevance: 0.96 },
                      { term: 'attention mechanism', frequency: 32, relevance: 0.89 },
                      { term: 'gradient descent', frequency: 28, relevance: 0.87 },
                      { term: 'backpropagation', frequency: 24, relevance: 0.86 },
                      { term: 'convolutional', frequency: 19, relevance: 0.78 }
                    ],
                    expertiseLevel: 0.85
                  }
                }
              };
              
              state.projects.push(demoProject);
              state.currentProject = demoProject;
              
              // Initialize knowledge collections
              state.knowledgeCollections = [
                {
                  id: 'kc1',
                  name: 'ML Fundamentals',
                  description: 'Core machine learning concepts and algorithms',
                  documentsCount: 23,
                  conceptsCount: 45,
                  lastUpdated: new Date(),
                  tags: ['machine-learning', 'fundamentals', 'algorithms']
                },
                {
                  id: 'kc2',
                  name: 'Research Papers',
                  description: 'Latest research in AI and ML',
                  documentsCount: 67,
                  conceptsCount: 128,
                  lastUpdated: new Date(),
                  tags: ['research', 'papers', 'cutting-edge']
                }
              ];
              
              // Initialize knowledge graph
              state.knowledgeGraph = {
                id: 'kg1',
                nodes: [
                  { id: 'n1', label: 'Machine Learning', type: 'concept', weight: 0.95 },
                  { id: 'n2', label: 'Neural Networks', type: 'concept', weight: 0.88 },
                  { id: 'n3', label: 'Deep Learning', type: 'concept', weight: 0.91 }
                ],
                edges: [
                  { source: 'n1', target: 'n2', weight: 0.85, type: 'relates_to' },
                  { source: 'n2', target: 'n3', weight: 0.92, type: 'specialization' }
                ],
                metadata: {
                  created: new Date(),
                  lastUpdated: new Date(),
                  version: '1.0'
                }
              };
              
              // Add some learning events
              state.learningEvents = [
                {
                  id: 'le1',
                  type: 'concept_learned',
                  content: 'Mastered transformer architecture',
                  timestamp: new Date(),
                  confidence: 0.94,
                  source: 'research_paper_analysis'
                },
                {
                  id: 'le2',
                  type: 'connection_formed',
                  content: 'Connected attention mechanisms to transformer performance',
                  timestamp: new Date(),
                  confidence: 0.87,
                  source: 'knowledge_synthesis'
                }
              ];
              
              // Add some insights
              state.insights = [
                {
                  id: 'i1',
                  content: 'The combination of attention mechanisms and positional encoding significantly improves sequence modeling performance.',
                  confidence: 0.93,
                  category: 'pattern_recognition',
                  connections: ['transformer', 'attention', 'sequence_modeling'],
                  timestamp: new Date(),
                  impact: 'high'
                }
              ];
            }
          }),
          
          clearSession: () => set((state) => {
            state.currentProject = null;
            state.projectContext = null;
            state.intelligenceContext = null;
            state.suggestions = [];
            state.activeAgents = [];
            state.navigation = {
              currentModule: 'dashboard',
              moduleHistory: ['dashboard'],
              timestamp: Date.now()
            };
          })
        }))
      ),
      {
        name: 'lighthouse-storage',
        partialize: (state) => ({
          projects: state.projects,
          knowledgeCollections: state.knowledgeCollections,
          insights: state.insights,
          learningEvents: state.learningEvents
        })
      }
    )
  )
);