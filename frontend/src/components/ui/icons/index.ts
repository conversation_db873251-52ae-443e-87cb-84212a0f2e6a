import { AlarmClockIcon } from '@/icons/alarm-clock';
import { AlignCenterIcon } from '@/icons/align-center';
import { AlignHorizontalIcon } from '@/icons/align-horizontal';
import { AlignVerticalIcon } from '@/icons/align-vertical';
import { AngryIcon } from '@/icons/angry';
import { ArchiveIcon } from '@/icons/archive';
import { ArrowLeftIcon } from '@/icons/arrow-left';
import { ArrowRightIcon } from '@/icons/arrow-right';
import { AtSignIcon } from '@/icons/at-sign';
import { AttachFileIcon } from '@/icons/attach-file';
import { BadgePercentIcon } from '@/icons/badge-percent';
import { BellIcon } from '@/icons/bell';
import { BoldIcon } from '@/icons/bold';
import { BoneIcon } from '@/icons/bone';
import { CalendarCogIcon } from '@/icons/calendar-cog';
import { ChartPieIcon } from '@/icons/chart-pie';
import { ChartScatterIcon } from '@/icons/chart-scatter';
import { CircleCheckIcon } from '@/icons/circle-check';
import { CircleDollarSignIcon } from '@/icons/circle-dollar-sign';
import { ClockIcon } from '@/icons/clock';
import { CopyIcon } from '@/icons/copy';
import { CursorClickIcon } from '@/icons/cursor-click';
import { DeleteIcon } from '@/icons/delete';
import { DownloadIcon } from '@/icons/download';
import { DownvoteIcon } from '@/icons/downvote';
import { SquarePenIcon } from '@/icons/square-pen';
import { ExpandIcon } from '@/icons/expand';
import { FilePenLineIcon } from '@/icons/file-pen-line';
import { FileStackIcon } from '@/icons/file-stack';
import { FingerprintIcon } from '@/icons/fingerprint';
import { FrameIcon } from '@/icons/frame';
import { GaugeIcon } from '@/icons/gauge';
import { GitPullRequestIcon } from '@/icons/git-pull-request';
import { GithubIcon } from '@/icons/github';
import { GripIcon } from '@/icons/grip';
import { HandCoinsIcon } from '@/icons/hand-coins';
import { HomeIcon } from '@/icons/home';
import { ItalicIcon } from '@/icons/italic';
import { LanguagesIcon } from '@/icons/languages';
import { LayersIcon } from '@/icons/layers';
import { LinkIcon } from '@/icons/link';
import { MenuIcon } from '@/icons/menu';
import { PartyPopperIcon } from '@/icons/party-popper';
import { PenToolIcon } from '@/icons/pen-tool';
import { RefreshCCWIcon } from '@/icons/refresh-ccw';
import { RouteIcon } from '@/icons/route';
import { ScanTextIcon } from '@/icons/scan-text';
import { SettingsIcon } from '@/icons/settings';
import { SettingsGearIcon } from '@/icons/settings-gear';
import { SunIcon } from '@/icons/sun';
import { UnderlineIcon } from '@/icons/underline';
import { UndoIcon } from '@/icons/undo';
import { ConnectIcon } from '@/icons/connect';
import { UpvoteIcon } from '@/icons/upvote';
import { UsersIcon } from '@/icons/users';
import { VolumeIcon } from '@/icons/volume';
import { CartIcon } from '@/icons/cart';
import { StethoscopeIcon } from '@/icons/stethoscope';
import { EarthIcon } from '@/icons/earth';
import { WorkflowIcon } from '@/icons/workflow';
import { LogoutIcon } from '@/icons/logout';
import { CircleHelpIcon } from '@/icons/circle-help';
import { UserIcon } from '@/icons/user';
import { AudioLinesIcon } from '@/icons/audio-lines';
import { FlameIcon } from '@/icons/flame';
import { EyeOffIcon } from '@/icons/eye-off';
import { SquareStackIcon } from '@/icons/square-stack';
import { BadgeAlertIcon } from '@/icons/badge-alert';
import { MessageCircleIcon } from '@/icons/message-circle';
import { MessageCircleMoreIcon } from '@/icons/message-circle-more';
import { SearchIcon } from '@/icons/search';
import { ShieldCheckIcon } from '@/icons/shield-check';
import { TimerIcon } from '@/icons/timer';
import { BluetoothSearchingIcon } from '@/icons/bluetooth-searching';
import { BluetoothConnectedIcon } from '@/icons/bluetooth-connected';
import { BluetoothOffIcon } from '@/icons/bluetooth-off';
import { FlaskIcon } from '@/icons/flask';
import { SyringeIcon } from '@/icons/syringe';
import { AArrowDownIcon } from '@/icons/a-arrow-down';
import { CompassIcon } from '@/icons/compass';
import { TrendingDownIcon } from '@/icons/trending-down';
import { TrendingUpIcon } from '@/icons/trending-up';
import { TrendingUpDownIcon } from '@/icons/trending-up-down';
import { PlayIcon } from '@/icons/play';
import { PauseIcon } from '@/icons/pause';
import { ChevronsUpDownIcon } from '@/icons/chevrons-up-down';
import { ChevronsDownUpIcon } from '@/icons/chevrons-down-up';
import { ChevronsLeftRightIcon } from '@/icons/chevrons-left-right';
import { ChevronsRightLeftIcon } from '@/icons/chevrons-right-left';
import { CircleChevronDownIcon } from '@/icons/circle-chevron-down';
import { CircleChevronLeftIcon } from '@/icons/circle-chevron-left';
import { CircleChevronRightIcon } from '@/icons/circle-chevron-right';
import { CircleChevronUpIcon } from '@/icons/circle-chevron-up';
import { CircleDashedIcon } from '@/icons/circle-dashed';
import { CheckIcon } from '@/icons/check';
import { CheckCheckIcon } from '@/icons/check-check';
import { IdCardIcon } from '@/icons/id-card';
import { LoaderPinwheelIcon } from '@/icons/loader-pinwheel';
import { RockingChairIcon } from '@/icons/rocking-chair';
import { ChartColumnDecreasingIcon } from '@/icons/chart-column-decreasing';
import { ChartColumnIncreasingIcon } from '@/icons/chart-column-increasing';
import { ChartBarDecreasingIcon } from '@/icons/chart-bar-decreasing';
import { ChartBarIncreasingIcon } from '@/icons/chart-bar-increasing';
import { BananaIcon } from '@/icons/banana';
import { BanIcon } from '@/icons/ban';
import { WifiIcon } from '@/icons/wifi';
import { ChromeIcon } from '@/icons/chrome';
import { FigmaIcon } from '@/icons/figma';
import { FishSymbolIcon } from '@/icons/fish-symbol';
import { GitCommitVerticalIcon } from '@/icons/git-commit-vertical';
import { GitCommitHorizontalIcon } from '@/icons/git-commit-horizontal';
import { WaypointsIcon } from '@/icons/waypoints';
import { ShipIcon } from '@/icons/ship';
import { RollerCoasterIcon } from '@/icons/roller-coaster';
import { AirplaneIcon } from '@/icons/airplane';
import { DrumIcon } from '@/icons/drum';
import { TrainTrackIcon } from '@/icons/train-track';
import { SparklesIcon } from '@/icons/sparkles';
import { WebhookIcon } from '@/icons/webhook';
import { RabbitIcon } from '@/icons/rabbit';
import { CogIcon } from '@/icons/cog';
import { CpuIcon } from '@/icons/cpu';
import { RocketIcon } from '@/icons/rocket';
import { ActivityIcon } from '@/icons/activity';
import { MapPinIcon } from '@/icons/map-pin';
import { MapPinCheckInsideIcon } from '@/icons/map-pin-check-inside';
import { MapPinMinusInsideIcon } from '@/icons/map-pin-minus-inside';
import { MapPinPlusInsideIcon } from '@/icons/map-pin-plus-inside';
import { MapPinOffIcon } from '@/icons/map-pin-off';
import { MapPinCheckIcon } from '@/icons/map-pin-check';
import { MapPinHouseIcon } from '@/icons/map-pin-house';
import { MapPinMinusIcon } from '@/icons/map-pin-minus';
import { MapPinPlusIcon } from '@/icons/map-pin-plus';
import { MapPinXInsideIcon } from '@/icons/map-pin-x-inside';
import { BatteryFullIcon } from '@/icons/battery-full';
import { TerminalIcon } from '@/icons/terminal';
import { KeyboardIcon } from '@/icons/keyboard';
import { ClapIcon } from '@/icons/clap';
import { LayoutPanelTopIcon } from '@/icons/layout-panel-top';
import { BookTextIcon } from '@/icons/book-text';
import { ShowerHeadIcon } from '@/icons/shower-head';
import { TelescopeIcon } from '@/icons/telescope';
import { WindIcon } from '@/icons/wind';
import { CctvIcon } from '@/icons/cctv';
import { CoffeeIcon } from '@/icons/coffee';
import { ArrowDownZAIcon } from '@/icons/arrow-down-z-a';
import { ArrowDownAZIcon } from '@/icons/arrow-down-a-z';
import { ArrowDown01con } from '@/icons/arrow-down-0-1';
import { ArrowDown10Icon } from '@/icons/arrow-down-1-0';
import { ClipboardCheckIcon } from '@/icons/clipboard-check';
import { FacebookIcon } from '@/icons/facebook';
import { LinkedinIcon } from '@/icons/linkedin';
import { TwitterIcon } from '@/icons/twitter';
import { YoutubeIcon } from '@/icons/youtube';
import { InstagramIcon } from '@/icons/instagram';
import { TwitchIcon } from '@/icons/twitch';
import { DribbbleIcon } from '@/icons/dribbble';
import { DiscordIcon } from '@/icons/discord';
import { XIcon } from '@/icons/x';
import { MoonIcon } from '@/icons/moon';
import { VibrateIcon } from '@/icons/vibrate';
import { SmartphoneChargingIcon } from '@/icons/smartphone-charging';
import { CastIcon } from '@/icons/cast';
import { UploadIcon } from '@/icons/upload';
import { CloudSunIcon } from '@/icons/cloud-sun';
import { SunsetIcon } from '@/icons/sunset';
import { SunDimIcon } from '@/icons/sun-dim';
import { SunMediumIcon } from '@/icons/sun-medium';
import { SunMoonIcon } from '@/icons/sun-moon';
import { MessageSquareIcon } from '@/icons/message-square';
import { MessageSquareMoreIcon } from '@/icons/message-square-more';
import { MessageCircleDashedIcon } from '@/icons/message-circle-dashed';
import { MessageSquareDashedIcon } from '@/icons/message-square-dashed';
import { AArrowUpIcon } from '@/icons/a-arrow-up';
import { FileCogIcon } from '@/icons/file-cog';
import { CalendarDaysIcon } from '@/icons/calendar-days';
import { ArrowBigDownDashIcon } from '@/icons/arrow-big-down-dash';
import { ArrowBigLeftDashIcon } from '@/icons/arrow-big-left-dash';
import { ArrowBigRightDashIcon } from '@/icons/arrow-big-right-dash';
import { ArrowBigUpDashIcon } from '@/icons/arrow-big-up-dash';
import { ArrowDownIcon } from '@/icons/arrow-down';
import { ArrowUpIcon } from '@/icons/arrow-up';
import { ArrowBigDownIcon } from '@/icons/arrow-big-down';
import { ArrowBigLeftIcon } from '@/icons/arrow-big-left';
import { ArrowBigRightIcon } from '@/icons/arrow-big-right';
import { ArrowBigUpIcon } from '@/icons/arrow-big-up';
import { KeyIcon } from '@/icons/key';
import { KeyCircleIcon } from '@/icons/key-circle';
import { KeySquareIcon } from '@/icons/key-square';
import { ChartLineIcon } from '@/icons/chart-line';
import { ChartSplineIcon } from '@/icons/chart-spline';
import { FileChartLineIcon } from '@/icons/file-chart-line';
import { ChartNoAxesColumnIncreasingIcon } from '@/icons/chart-no-axes-column-increasing';
import { ChartNoAxesColumnDecreasingIcon } from '@/icons/chart-no-axes-column-decreasing';
import { RadioIcon } from '@/icons/radio';
import { RadioTowerIcon } from '@/icons/radio-tower';
import { AirVentIcon } from '@/icons/air-vent';
import { TornadoIcon } from '@/icons/tornado';
import { WindArrowDownIcon } from '@/icons/wind-arrow-down';
import { CloudRainIcon } from '@/icons/cloud-rain';
import { CloudRainWindIcon } from '@/icons/cloud-rain-wind';
import { WavesIcon } from '@/icons/waves';
import { WavesLadderIcon } from '@/icons/waves-ladder';
import { SquareArrowDownIcon } from '@/icons/square-arrow-down';
import { SquareArrowLeftIcon } from '@/icons/square-arrow-left';
import { SquareArrowUpIcon } from '@/icons/square-arrow-up';
import { SquareArrowRightIcon } from '@/icons/square-arrow-right';
import { BlocksIcon } from '@/icons/blocks';
import { CalendarCheckIcon } from '@/icons/calendar-check';
import { CalendarCheck2Icon } from '@/icons/calendar-check-2';
import { FileCheckIcon } from '@/icons/file-check';
import { FileCheck2Icon } from '@/icons/file-check-2';
import { MailCheckIcon } from '@/icons/mail-check';
import { MonitorCheckIcon } from '@/icons/monitor-check';
import { LaptopMinimalCheckIcon } from '@/icons/laptop-minimal-check';
import { ChevronDownIcon } from '@/icons/chevron-down';
import { ChevronUpIcon } from '@/icons/chevron-up';
import { ChevronLeftIcon } from '@/icons/chevron-left';
import { ChevronRightIcon } from '@/icons/chevron-right';
import { SquareChevronDownIcon } from '@/icons/square-chevron-down';
import { SquareChevronUpIcon } from '@/icons/square-chevron-up';
import { SquareChevronRightIcon } from '@/icons/square-chevron-right';
import { SquareChevronLeftIcon } from '@/icons/square-chevron-left';
import { GalleryHorizontalEndIcon } from '@/icons/gallery-horizontal-end';
import { GalleryVerticalEndIcon } from '@/icons/gallery-vertical-end';
import { HandHeartIcon } from '@/icons/hand-heart';
import { SquareActivityIcon } from '@/icons/square-activity';
import { RotateCWIcon } from '@/icons/rotate-cw';
import { RotateCCWIcon } from '@/icons/rotate-ccw';
import { GalleryThumbnailsIcon } from '@/icons/gallery-thumbnails';
import { UserCheckIcon } from '@/icons/user-check';
import { UserRoundCheckIcon } from '@/icons/user-round-check';
import { BoxesIcon } from '@/icons/boxes';
import { RefreshCWIcon } from '@/icons/refresh-cw';
import { RefreshCCWDotIcon } from '@/icons/refresh-ccw-dot';
import { RefreshCWOffIcon } from '@/icons/refresh-cw-off';
import { RedoIcon } from '@/icons/redo';
import { UndoDotIcon } from '@/icons/undo-dot';
import { RedoDotIcon } from '@/icons/redo-dot';
import { ScanFaceIcon } from '@/icons/scan-face';
import { FrownIcon } from '@/icons/frown';
import { SmilePlusIcon } from '@/icons/smile-plus';
import { SmileIcon } from '@/icons/smile';
import { LaughIcon } from '@/icons/laugh';
import { AnnoyedIcon } from '@/icons/annoyed';
import { MehIcon } from '@/icons/meh';
import { HistoryIcon } from '@/icons/history';
import { FileTextIcon } from '@/icons/file-text';
import { UserRoundPlusIcon } from '@/icons/user-round-plus';
import { PanelLeftOpenIcon } from '@/icons/panel-left-open';
import { PanelLeftCloseIcon } from '@/icons/panel-left-close';
import { PanelRightOpenIcon } from '@/icons/panel-right-open';
import { HeartIcon } from './heart';
import { PlusIcon } from './plus';

type IconListItem = {
  name: string;
  icon: React.ElementType;
  keywords: string[];
};

const ICON_LIST: IconListItem[] = [
  {
    name: 'plus',
    icon: PlusIcon,
    keywords: [
      'add',
      'new',
      'increase',
      'increment',
      'positive',
      'calculate',
      'toolbar',
      'crosshair',
      'aim',
      'target',
      'scope',
      'sight',
      'reticule',
      'maximum',
      'upgrade',
      'extra',
      '+',
    ],
  },
  {
    name: 'heart',
    icon: HeartIcon,
    keywords: ['like', 'love', 'emotion', 'suit', 'playing', 'cards'],
  },
  {
    name: 'smartphone-charging',
    icon: SmartphoneChargingIcon,
    keywords: ['phone', 'cellphone', 'device', 'power', 'screen'],
  },
  {
    name: 'history',
    icon: HistoryIcon,
    keywords: ['history', 'back', 'previous', 'arrow'],
  },
  { name: 'square-activity', icon: SquareActivityIcon, keywords: ['activity'] },
  {
    name: 'square-arrow-down',
    icon: SquareArrowDownIcon,
    keywords: ['arrow', 'down', 'a'],
  },
  {
    name: 'square-arrow-left',
    icon: SquareArrowLeftIcon,
    keywords: ['arrow', 'left', 'a'],
  },
  {
    name: 'square-arrow-right',
    icon: SquareArrowRightIcon,
    keywords: ['arrow', 'right', 'a'],
  },
  {
    name: 'square-arrow-up',
    icon: SquareArrowUpIcon,
    keywords: ['arrow', 'up', 'a'],
  },
  {
    name: 'scan-face',
    icon: ScanFaceIcon,
    keywords: ['scan', 'face', 'emotion'],
  },
  {
    name: 'frown',
    icon: FrownIcon,
    keywords: ['frown', 'emotion', 'face', 'sad'],
  },
  {
    name: 'smile-plus',
    icon: SmilePlusIcon,
    keywords: ['smile', 'plus', 'emotion', 'face'],
  },
  { name: 'smile', icon: SmileIcon, keywords: ['smile', 'emotion', 'face'] },
  { name: 'laugh', icon: LaughIcon, keywords: ['laugh', 'emotion', 'face'] },
  {
    name: 'annoyed',
    icon: AnnoyedIcon,
    keywords: ['annoyed', 'emotion', 'face'],
  },
  { name: 'meh', icon: MehIcon, keywords: ['meh', 'emotion', 'face'] },
  {
    name: 'key',
    icon: KeyIcon,
    keywords: ['key', 'authentication', 'security', 'access', 'password'],
  },
  {
    name: 'key-square',
    icon: KeySquareIcon,
    keywords: ['key', 'authentication', 'security', 'access', 'password'],
  },
  {
    name: 'key-circle',
    icon: KeyCircleIcon,
    keywords: ['key', 'authentication', 'security', 'access', 'password'],
  },
  {
    name: 'rotate-cw',
    icon: RotateCWIcon,
    keywords: [
      'rotate',
      'clockwise',
      'turn',
      'degrees',
      'degrees',
      'clockwise',
      'counterclockwise',
    ],
  },
  {
    name: 'rotate-ccw',
    icon: RotateCCWIcon,
    keywords: [
      'rotate',
      'counterclockwise',
      'turn',
      'degrees',
      'degrees',
      'clockwise',
      'counterclockwise',
    ],
  },
  {
    name: 'refresh-cw-off',
    icon: RefreshCWOffIcon,
    keywords: ['refresh', 'rotate', 'reload', 'rerun', 'circular', 'cycle'],
  },
  {
    name: 'refresh-cw',
    icon: RefreshCWIcon,
    keywords: ['refresh', 'rotate', 'reload', 'rerun', 'circular', 'cycle'],
  },
  {
    name: 'refresh-ccw-dot',
    icon: RefreshCCWDotIcon,
    keywords: ['refresh', 'rotate', 'reload', 'rerun', 'circular', 'cycle'],
  },
  {
    name: 'redo',
    icon: RedoIcon,
    keywords: ['redo', 'repeat', 'undo', 'back', 'previous', 'arrow'],
  },
  {
    name: 'undo-dot',
    icon: UndoDotIcon,
    keywords: ['undo', 'repeat', 'back', 'previous', 'arrow'],
  },
  {
    name: 'redo-dot',
    icon: RedoDotIcon,
    keywords: ['redo', 'repeat', 'undo', 'back', 'previous', 'arrow'],
  },
  {
    name: 'arrow-big-down',
    icon: ArrowBigDownIcon,
    keywords: ['arrow', 'down', 'big', 'below', 'south', 'bottom'],
  },
  {
    name: 'arrow-big-left',
    icon: ArrowBigLeftIcon,
    keywords: ['arrow', 'left', 'big', 'west', 'previous', '<-'],
  },
  {
    name: 'arrow-big-right',
    icon: ArrowBigRightIcon,
    keywords: ['arrow', 'right', 'big', 'east', 'next', '->'],
  },
  {
    name: 'arrow-big-up',
    icon: ArrowBigUpIcon,
    keywords: ['arrow', 'up', 'big', 'north', 'top'],
  },
  {
    name: 'a-arrow-up',
    icon: AArrowUpIcon,
    keywords: ['arrow', 'up', 'a'],
  },
  {
    name: 'chart-spline',
    icon: ChartSplineIcon,
    keywords: [
      'chart',
      'spline',
      'graph',
      'statistics',
      'analytics',
      'diagram',
      'presentation',
      'analytics',
    ],
  },
  {
    name: 'arrow-up',
    icon: ArrowUpIcon,
    keywords: ['up', 'above', 'direction', 'north', 'top'],
  },
  {
    name: 'arrow-down',
    icon: ArrowDownIcon,
    keywords: ['down', 'below', 'direction', 'south', 'bottom'],
  },
  {
    name: 'vibrate',
    icon: VibrateIcon,
    keywords: [
      'smartphone',
      'notification',
      'rumble',
      'haptic feedback',
      'screen',
    ],
  },
  {
    name: 'waves-ladder',
    icon: WavesLadderIcon,
    keywords: ['waves', 'ladder', 'water', 'ocean', 'sea'],
  },
  {
    name: 'waves',
    icon: WavesIcon,
    keywords: ['waves', 'water', 'ocean', 'sea', 'wave', 'sea wave'],
  },
  {
    name: 'wind-arrow-down',
    icon: WindArrowDownIcon,
    keywords: ['wind', 'arrow', 'down', 'wind arrow down'],
  },
  {
    name: 'air-vent',
    icon: AirVentIcon,
    keywords: ['air', 'vent', 'fan', 'air conditioning', 'wind', 'cooling'],
  },
  {
    name: 'tornado',
    icon: TornadoIcon,
    keywords: ['tornado', 'wind', 'weather', 'spin', 'twister', 'whirlwind'],
  },
  {
    name: 'cloud-rain',
    icon: CloudRainIcon,
    keywords: ['cloud', 'rain', 'weather', 'cloud rain'],
  },
  {
    name: 'cloud-rain-wind',
    icon: CloudRainWindIcon,
    keywords: ['cloud', 'rain', 'wind', 'weather', 'cloud rain wind'],
  },
  {
    name: 'arrow-big-down-dash',
    icon: ArrowBigDownDashIcon,
    keywords: ['arrow', 'down', 'big', 'below', 'south', 'bottom', 'dash'],
  },
  {
    name: 'arrow-big-left-dash',
    icon: ArrowBigLeftDashIcon,
    keywords: ['arrow', 'left', 'big', 'below', 'west', 'dash'],
  },
  {
    name: 'arrow-big-right-dash',
    icon: ArrowBigRightDashIcon,
    keywords: ['arrow', 'right', 'big', 'below', 'east', 'dash'],
  },
  {
    name: 'arrow-big-up-dash',
    icon: ArrowBigUpDashIcon,
    keywords: ['arrow', 'up', 'big', 'above', 'north', 'dash'],
  },
  {
    name: 'moon',
    icon: MoonIcon,
    keywords: ['night', 'dark', 'moon'],
  },
  {
    name: 'facebook',
    icon: FacebookIcon,
    keywords: ['social', 'network', 'facebook'],
  },
  {
    name: 'twitter',
    icon: TwitterIcon,
    keywords: ['social', 'network', 'twitter', 'X'],
  },
  {
    name: 'linkedin',
    icon: LinkedinIcon,
    keywords: ['social', 'network', 'linkedin'],
  },
  {
    name: 'youtube',
    icon: YoutubeIcon,
    keywords: ['social', 'network', 'youtube'],
  },
  {
    name: 'instagram',
    icon: InstagramIcon,
    keywords: ['social', 'network', 'instagram'],
  },
  {
    name: 'twitch',
    icon: TwitchIcon,
    keywords: ['social', 'network', 'twitch'],
  },
  {
    name: 'dribbble',
    icon: DribbbleIcon,
    keywords: ['social', 'network', 'dribbble'],
  },
  {
    name: 'discord',
    icon: DiscordIcon,
    keywords: ['social', 'network', 'discord'],
  },
  {
    name: 'search',
    icon: SearchIcon,
    keywords: ['find', 'scan', 'magnifier', 'magnifying glass'],
  },
  {
    name: 'cloud-sun',
    icon: CloudSunIcon,
    keywords: ['weather', 'sun', 'cloud', 'light', 'bright'],
  },
  {
    name: 'sunset',
    icon: SunsetIcon,
    keywords: ['sun', 'sunset', 'weather', 'bright', 'dawn', 'evening'],
  },
  {
    name: 'sun-dim',
    icon: SunDimIcon,
    keywords: ['sun', 'dim', 'weather', 'light', 'day'],
  },
  {
    name: 'sun-medium',
    icon: SunMediumIcon,
    keywords: ['sun', 'medium', 'weather', 'light', 'day'],
  },
  {
    name: 'sun-moon',
    icon: SunMoonIcon,
    keywords: ['sun', 'moon', 'day', 'night', 'evening'],
  },
  {
    name: 'cart',
    icon: CartIcon,
    keywords: [
      'trolley',
      'cart',
      'basket',
      'e-commerce',
      'store',
      'purchase',
      'products',
      'items',
      'ingredients',
    ],
  },
  {
    name: 'stethoscope',
    icon: StethoscopeIcon,
    keywords: ['phonendoscope', 'medical', 'heart', 'lungs', 'sound'],
  },
  {
    name: 'circle-check',
    icon: CircleCheckIcon,
    keywords: ['done', 'todo', 'tick', 'complete', 'task'],
  },
  {
    name: 'earth',
    icon: EarthIcon,
    keywords: ['world', 'browser', 'language', 'translate', 'globe'],
  },
  {
    name: 'workflow',
    icon: WorkflowIcon,
    keywords: [
      'action',
      'continuous integration',
      'ci',
      'automation',
      'devops',
      'network',
      'node',
      'connection',
    ],
  },
  {
    name: 'logout',
    icon: LogoutIcon,
    keywords: ['sign out', 'arrow', 'exit', 'auth'],
  },
  {
    name: 'circle-help',
    icon: CircleHelpIcon,
    keywords: ['question mark'],
  },
  {
    name: 'user',
    icon: UserIcon,
    keywords: ['person', 'account', 'contact'],
  },
  {
    name: 'flame',
    icon: FlameIcon,
    keywords: ['fire', 'flame', 'hot'],
  },
  {
    name: 'audio-lines',
    icon: AudioLinesIcon,
    keywords: [
      'graphic equaliser',
      'sound',
      'noise',
      'listen',
      'hearing',
      'hertz',
      'frequency',
      'wavelength',
      'vibrate',
      'sine',
      'synthesizer',
      'synthesiser',
      'levels',
      'track',
      'music',
      'playback',
      'radio',
      'broadcast',
      'airwaves',
      'voice',
      'vocals',
      'singer',
      'song',
    ],
  },
  {
    name: 'eye-off',
    icon: EyeOffIcon,
    keywords: [
      'view',
      'watch',
      'see',
      'hide',
      'conceal',
      'mask',
      'hidden',
      'visibility',
      'vision',
    ],
  },
  {
    name: 'square-stack',
    icon: SquareStackIcon,
    keywords: [
      'versions',
      'clone',
      'copy',
      'duplicate',
      'multiple',
      'revisions',
      'version control',
      'backup',
      'history',
    ],
  },
  {
    name: 'badge-alert',
    icon: BadgeAlertIcon,
    keywords: [
      'check',
      'verified',
      'unverified',
      'security',
      'safety',
      'issue',
    ],
  },
  {
    name: 'message-circle',
    icon: MessageCircleIcon,
    keywords: [
      'comment',
      'chat',
      'conversation',
      'dialog',
      'feedback',
      'speech bubble',
    ],
  },
  {
    name: 'message-circle-more',
    icon: MessageCircleMoreIcon,
    keywords: [
      'comment',
      'chat',
      'conversation',
      'dialog',
      'feedback',
      'speech bubble',
      'typing',
      'writing',
      'responding',
      'ellipsis',
      'etc',
    ],
  },
  {
    name: 'message-square',
    icon: MessageSquareIcon,
    keywords: [
      'comment',
      'chat',
      'conversation',
      'dialog',
      'feedback',
      'speech bubble',
      'message',
    ],
  },
  {
    name: 'message-square-more',
    icon: MessageSquareMoreIcon,
    keywords: [
      'comment',
      'chat',
      'conversation',
      'dialog',
      'feedback',
      'speech bubble',
      'typing',
      'writing',
      'message',
      'responding',
      'ellipsis',
      'etc',
    ],
  },
  {
    name: 'message-circle-dashed',
    icon: MessageCircleDashedIcon,
    keywords: [
      'comment',
      'chat',
      'conversation',
      'dialog',
      'feedback',
      'speech bubble',
      'typing',
      'writing',
      'message',
      'responding',
      'ellipsis',
      'etc',
    ],
  },
  {
    name: 'message-square-dashed',
    icon: MessageSquareDashedIcon,
    keywords: [
      'comment',
      'chat',
      'conversation',
      'dialog',
      'feedback',
      'speech bubble',
      'typing',
      'writing',
      'message',
      'responding',
      'ellipsis',
      'etc',
    ],
  },
  {
    name: 'clipboard-check',
    icon: ClipboardCheckIcon,
    keywords: ['clipboard', 'check', 'clipboard-check', 'clipboard-checkmark'],
  },
  {
    name: 'home',
    icon: HomeIcon,
    keywords: ['home', 'living', 'building', 'residence', 'architecture'],
  },
  {
    name: 'arrow-left',
    icon: ArrowLeftIcon,
    keywords: ['previous', 'back', 'direction', 'west', '<-'],
  },
  {
    name: 'arrow-right',
    icon: ArrowRightIcon,
    keywords: ['forward', 'next', 'direction', 'east', '->'],
  },
  {
    name: 'expand',
    icon: ExpandIcon,
    keywords: ['scale', 'fullscreen'],
  },
  {
    name: 'route',
    icon: RouteIcon,
    keywords: ['path', 'journey', 'planner', 'points', 'stops', 'stations'],
  },
  {
    name: 'airplane',
    icon: AirplaneIcon,
    keywords: [
      'flight',
      'path',
      'journey',
      'planner',
      'points',
      'stops',
      'stations',
    ],
  },
  {
    name: 'refresh-ccw',
    icon: RefreshCCWIcon,
    keywords: [
      'arrows',
      'rotate',
      'reload',
      'rerun',
      'synchronise',
      'synchronize',
      'circular',
      'cycle',
    ],
  },
  { name: 'undo', icon: UndoIcon, keywords: ['redo', 'rerun', 'history'] },
  {
    name: 'download',
    icon: DownloadIcon,
    keywords: ['import', 'export', 'save'],
  },
  {
    name: 'square-pen',
    icon: SquarePenIcon,
    keywords: [
      'pencil',
      'change',
      'create',
      'draw',
      'sketch',
      'draft',
      'writer',
      'writing',
      'biro',
      'ink',
      'marker',
      'felt tip',
      'stationery',
      'artist',
    ],
  },
  {
    name: 'delete',
    icon: DeleteIcon,
    keywords: ['garbage', 'delete', 'remove', 'bin'],
  },
  {
    name: 'settings',
    icon: SettingsIcon,
    keywords: ['settings', 'filters', 'controls'],
  },
  {
    name: 'settings-gear',
    icon: SettingsGearIcon,
    keywords: ['cog', 'edit', 'gear', 'preferences'],
  },
  {
    name: 'calendar-cog',
    icon: CalendarCogIcon,
    keywords: [
      'date',
      'day',
      'month',
      'year',
      'events',
      'settings',
      'gear',
      'cog',
    ],
  },
  {
    name: 'cursor-click',
    icon: CursorClickIcon,
    keywords: ['click', 'select'],
  },
  {
    name: 'menu',
    icon: MenuIcon,
    keywords: ['bars', 'navigation', 'hamburger', 'options'],
  },
  {
    name: 'clock',
    icon: ClockIcon,
    keywords: ['time', 'watch', 'alarm'],
  },
  {
    name: 'file-stack',
    icon: FileStackIcon,
    keywords: [
      'versions',
      'multiple',
      'copy',
      'documents',
      'revisions',
      'version control',
      'history',
    ],
  },
  {
    name: 'file-pen-line',
    icon: FilePenLineIcon,
    keywords: ['edit'],
  },
  {
    name: 'archive',
    icon: ArchiveIcon,
    keywords: ['index', 'backup', 'box', 'storage', 'records'],
  },
  {
    name: 'copy',
    icon: CopyIcon,
    keywords: ['clone', 'duplicate', 'multiple'],
  },
  {
    name: 'attach-file',
    icon: AttachFileIcon,
    keywords: ['attachment', 'file'],
  },
  { name: 'alarm-clock', icon: AlarmClockIcon, keywords: ['morning'] },
  { name: 'bold', icon: BoldIcon, keywords: ['text', 'strong', 'format'] },
  {
    name: 'italic',
    icon: ItalicIcon,
    keywords: ['oblique', 'text', 'format'],
  },
  {
    name: 'underline',
    icon: UnderlineIcon,
    keywords: ['text', 'format'],
  },
  {
    name: 'scan-text',
    icon: ScanTextIcon,
    keywords: ['recognition', 'read', 'translate', 'copy', 'lines'],
  },
  {
    name: 'languages',
    icon: LanguagesIcon,
    keywords: ['translate'],
  },
  {
    name: 'at-sign',
    icon: AtSignIcon,
    keywords: ['mention', 'at', 'email', 'message', '@'],
  },
  {
    name: 'bell',
    icon: BellIcon,
    keywords: ['alarm', 'notification', 'sound', 'reminder'],
  },
  {
    name: 'users',
    icon: UsersIcon,
    keywords: ['group', 'people'],
  },
  {
    name: 'upvote',
    icon: UpvoteIcon,
    keywords: ['like', 'good', 'emotion'],
  },
  {
    name: 'downvote',
    icon: DownvoteIcon,
    keywords: ['dislike', 'bad', 'emotion'],
  },
  {
    name: 'circle-dollar-sign',
    icon: CircleDollarSignIcon,
    keywords: ['monetization', 'marketing', 'currency', 'money', 'payment'],
  },
  {
    name: 'hand-coins',
    icon: HandCoinsIcon,
    keywords: [
      'savings',
      'banking',
      'money',
      'finance',
      'offers',
      'mortgage',
      'payment',
      'received',
      'wage',
      'payroll',
      'allowance',
      'pocket money',
      'handout',
      'pennies',
    ],
  },
  {
    name: 'badge-percent',
    icon: BadgePercentIcon,
    keywords: [
      'verified',
      'unverified',
      'sale',
      'discount',
      'offer',
      'marketing',
      'sticker',
      'price tag',
    ],
  },
  {
    name: 'chart-pie',
    icon: ChartPieIcon,
    keywords: ['statistics', 'analytics', 'diagram', 'presentation'],
  },
  {
    name: 'chart-scatter',
    icon: ChartScatterIcon,
    keywords: ['chart', 'analytics', 'scatter'],
  },
  {
    name: 'gauge',
    icon: GaugeIcon,
    keywords: [
      'dashboard',
      'dial',
      'meter',
      'speed',
      'pressure',
      'measure',
      'level',
    ],
  },
  {
    name: 'pen-tool',
    icon: PenToolIcon,
    keywords: ['vector', 'drawing', 'path'],
  },
  {
    name: 'fingerprint',
    icon: FingerprintIcon,
    keywords: ['2fa', 'authentication', 'biometric', 'identity', 'security'],
  },
  {
    name: 'link',
    icon: LinkIcon,
    keywords: ['chain', 'url'],
  },
  {
    name: 'layers',
    icon: LayersIcon,
    keywords: [
      'stack',
      'pile',
      'pages',
      'sheets',
      'paperwork',
      'copies',
      'copy',
    ],
  },
  {
    name: 'grip',
    icon: GripIcon,
    keywords: ['grab', 'dots', 'handle', 'move', 'drag'],
  },
  {
    name: 'git-pull-request',
    icon: GitPullRequestIcon,
    keywords: ['code', 'version control', 'open'],
  },
  {
    name: 'github',
    icon: GithubIcon,
    keywords: ['logo', 'version control', 'repository', 'source code', 'git'],
  },
  {
    name: 'connect',
    icon: ConnectIcon,
    keywords: [
      'electricity',
      'energy',
      'electronics',
      'socket',
      'outlet',
      'disconnect',
    ],
  },
  {
    name: 'volume',
    icon: VolumeIcon,
    keywords: ['music', 'sound', 'mute', 'speaker'],
  },
  {
    name: 'sun',
    icon: SunIcon,
    keywords: ['brightness', 'weather', 'light', 'summer'],
  },
  {
    name: 'party-popper',
    icon: PartyPopperIcon,
    keywords: [
      'emoji',
      'congratulations',
      'celebration',
      'party',
      'tada',
      '🎉',
      '🎊',
      'excitement',
      'exciting',
      'excites',
      'confetti',
    ],
  },
  {
    name: 'frame',
    icon: FrameIcon,
    keywords: ['logo', 'design', 'tool'],
  },
  {
    name: 'bone',
    icon: BoneIcon,
    keywords: ['health', 'skeleton', 'skull', 'death', 'pets', 'dog'],
  },
  {
    name: 'align-center',
    icon: AlignCenterIcon,
    keywords: ['text', 'alignment', 'center'],
  },
  {
    name: 'align-vertical',
    icon: AlignVerticalIcon,
    keywords: ['center', 'items', 'flex', 'justify', 'distribute', 'between'],
  },
  {
    name: 'align-horizontal',
    icon: AlignHorizontalIcon,
    keywords: ['center', 'items', 'flex', 'justify', 'distribute', 'between'],
  },
  {
    name: 'compass',
    icon: CompassIcon,
    keywords: [
      'direction',
      'navigation',
      'north',
      'south',
      'east',
      'west',
      'compass',
    ],
  },
  {
    name: 'shield-check',
    icon: ShieldCheckIcon,
    keywords: ['security', 'verified', 'safe', 'protection', 'secure'],
  },
  {
    name: 'trending-down',
    icon: TrendingDownIcon,
    keywords: ['down', 'trend', 'graph', 'downward', 'decrease', 'statistics'],
  },
  {
    name: 'trending-up',
    icon: TrendingUpIcon,
    keywords: ['up', 'trend', 'graph', 'upward', 'increase', 'statistics'],
  },
  {
    name: 'trending-up-down',
    icon: TrendingUpDownIcon,
    keywords: [
      'up',
      'trend',
      'graph',
      'upward',
      'increase',
      'statistics',
      'down',
      'trend',
      'graph',
      'downward',
      'decrease',
    ],
  },
  {
    name: 'timer',
    icon: TimerIcon,
    keywords: ['time', 'watch', 'alarm', 'stopwatch', 'stopwatch'],
  },
  {
    name: 'bluetooth-searching',
    icon: BluetoothSearchingIcon,
    keywords: ['tool', 'connection', 'network'],
  },
  {
    name: 'bluetooth-connected',
    icon: BluetoothConnectedIcon,
    keywords: ['tool', 'connection', 'network'],
  },
  {
    name: 'bluetooth-off',
    icon: BluetoothOffIcon,
    keywords: ['tool', 'connection', 'network'],
  },
  {
    name: 'flask',
    icon: FlaskIcon,
    keywords: [
      'beaker',
      'erlenmeyer',
      'lab',
      'chemistry',
      'experiment',
      'test',
    ],
  },
  {
    name: 'syringe',
    icon: SyringeIcon,
    keywords: [
      'medicine',
      'medical',
      'needle',
      'pump',
      'plunger',
      'nozzle',
      'blood',
    ],
  },
  {
    name: 'play',
    icon: PlayIcon,
    keywords: ['video', 'play', 'start'],
  },
  {
    name: 'pause',
    icon: PauseIcon,
    keywords: ['video', 'pause', 'stop'],
  },
  {
    name: 'chart-column-decreasing',
    icon: ChartColumnDecreasingIcon,
    keywords: ['chart', 'column', 'decreasing'],
  },
  {
    name: 'chart-column-increasing',
    icon: ChartColumnIncreasingIcon,
    keywords: ['chart', 'column', 'increasing'],
  },
  {
    name: 'chart-bar-decreasing',
    icon: ChartBarDecreasingIcon,
    keywords: ['chart', 'bar', 'decreasing'],
  },
  {
    name: 'chart-bar-increasing',
    icon: ChartBarIncreasingIcon,
    keywords: ['chart', 'bar', 'increasing'],
  },
  {
    name: 'chevron-down',
    icon: ChevronDownIcon,
    keywords: ['chevron', 'down', 'expand', 'unfold', 'vertical'],
  },
  {
    name: 'chevron-up',
    icon: ChevronUpIcon,
    keywords: ['chevron', 'up', 'collapse', 'fold', 'vertical'],
  },
  {
    name: 'chevron-left',
    icon: ChevronLeftIcon,
    keywords: ['chevron', 'left', 'previous', 'back', 'direction'],
  },
  {
    name: 'chevron-right',
    icon: ChevronRightIcon,
    keywords: ['chevron', 'right', 'next', 'forward', 'direction'],
  },
  {
    name: 'chevrons-up-down',
    icon: ChevronsUpDownIcon,
    keywords: ['expand', 'unfold', 'vertical', 'chevron'],
  },
  {
    name: 'chevrons-down-up',
    icon: ChevronsDownUpIcon,
    keywords: ['collapse', 'fold', 'vertical', 'chevron'],
  },
  {
    name: 'chevrons-left-right',
    icon: ChevronsLeftRightIcon,
    keywords: ['expand', 'unfold', 'horizontal', 'chevron'],
  },
  {
    name: 'chevrons-right-left',
    icon: ChevronsRightLeftIcon,
    keywords: ['collapse', 'fold', 'horizontal', 'chevron'],
  },
  {
    name: 'circle-chevron-down',
    icon: CircleChevronDownIcon,
    keywords: ['back', 'menu', 'chevron'],
  },
  {
    name: 'circle-chevron-left',
    icon: CircleChevronLeftIcon,
    keywords: [
      'back',
      'previous',
      'less than',
      'fewer',
      'menu',
      '<',
      'chevron',
    ],
  },
  {
    name: 'circle-chevron-right',
    icon: CircleChevronRightIcon,
    keywords: [
      'next',
      'forward',
      'more than',
      'greater',
      'menu',
      '>',
      'chevron',
    ],
  },
  {
    name: 'square-chevron-down',
    icon: SquareChevronDownIcon,
    keywords: ['chevron', 'down', 'expand'],
  },
  {
    name: 'square-chevron-up',
    icon: SquareChevronUpIcon,
    keywords: ['chevron', 'up', 'collapse'],
  },
  {
    name: 'square-chevron-right',
    icon: SquareChevronRightIcon,
    keywords: ['chevron', 'right', 'next'],
  },
  {
    name: 'square-chevron-left',
    icon: SquareChevronLeftIcon,
    keywords: ['chevron', 'left', 'previous'],
  },
  {
    name: 'a-arrow-down',
    icon: AArrowDownIcon,
    keywords: ['arrow', 'down', 'a'],
  },
  {
    name: 'circle-dashed',
    icon: CircleDashedIcon,
    keywords: ['dashed', 'circle', 'dashed circle'],
  },
  {
    name: 'circle-chevron-up',
    icon: CircleChevronUpIcon,
    keywords: ['caret', 'ahead', 'forward', 'menu', 'chevron'],
  },
  {
    name: 'check',
    icon: CheckIcon,
    keywords: ['done', 'todo', 'tick', 'complete', 'task'],
  },
  {
    name: 'check-check',
    icon: CheckCheckIcon,
    keywords: [
      'done',
      'received',
      'double',
      'todo',
      'tick',
      'complete',
      'task',
    ],
  },
  {
    name: 'id-card',
    icon: IdCardIcon,
    keywords: [
      'identification',
      'personal',
      'details',
      'profile',
      'card',
      'badge',
      'identity',
      'authentication',
      'secure',
    ],
  },
  {
    name: 'loader-pinwheel',
    icon: LoaderPinwheelIcon,
    keywords: [
      'loading',
      'wait',
      'busy',
      'progress',
      'throbber',
      'spinner',
      'spinning',
      'beach ball',
      'frozen',
      'freeze',
    ],
  },
  {
    name: 'rocking-chair',
    icon: RockingChairIcon,
    keywords: ['furniture', 'seat', 'rock', 'relax', 'sit', 'chair'],
  },
  {
    name: 'banana',
    icon: BananaIcon,
    keywords: ['fruit', 'food', 'yellow', 'tropical'],
  },
  {
    name: 'wifi',
    icon: WifiIcon,
    keywords: ['internet', 'electronic', 'connection', 'data'],
  },
  {
    name: 'chrome',
    icon: ChromeIcon,
    keywords: ['browser', 'google', 'internet', 'web', 'logo'],
  },
  {
    name: 'figma',
    icon: FigmaIcon,
    keywords: ['design', 'tool', 'logo'],
  },
  {
    name: 'fish-symbol',
    icon: FishSymbolIcon,
    keywords: [
      'fish',
      'seafood',
      'ocean',
      'marine',
      'dish',
      'restaurant',
      'course',
      'meal',
      'seafood',
      'pet',
      'sea',
      'marine',
    ],
  },
  {
    name: 'git-commit-vertical',
    icon: GitCommitVerticalIcon,
    keywords: [
      'code',
      'open',
      'version control ',
      'waypoint',
      'stop',
      'station',
    ],
  },
  {
    name: 'git-commit-horizontal',
    icon: GitCommitHorizontalIcon,
    keywords: [
      'code',
      'open',
      'version control ',
      'waypoint',
      'stop',
      'station',
    ],
  },
  {
    name: 'waypoints',
    icon: WaypointsIcon,
    keywords: [
      'indirection',
      'vpn',
      'virtual private network',
      'proxy',
      'connections',
      'bounce',
      'reroute',
      'path',
      'journey',
      'planner',
      'stops',
      'stations',
      'shared',
      'spread',
      'vira',
    ],
  },
  {
    name: 'ship',
    icon: ShipIcon,
    keywords: [
      'boat',
      'vessel',
      'sea',
      'ocean',
      'water',
      'transport',
      'knots',
      'nautical mile',
      'maritime',
      'sailing',
      'yacht',
      'cruise',
      'ocean liner',
      'tanker',
      'vessel',
      'navy',
      'trip',
    ],
  },
  {
    name: 'roller-coaster',
    icon: RollerCoasterIcon,
    keywords: [
      'ride',
      'fun',
      'amusement',
      'park',
      'thrill',
      'excitement',
      'attraction',
      'entertainment',
      'amusement park',
      'theme park',
      'funfair',
    ],
  },
  {
    name: 'drum',
    icon: DrumIcon,
    keywords: ['music', 'instrument', 'percussion', 'beat', 'rhythm', 'sound'],
  },
  {
    name: 'train-track',
    icon: TrainTrackIcon,
    keywords: ['railway', 'track', 'train', 'transport', 'travel', 'line'],
  },
  {
    name: 'webhook',
    icon: WebhookIcon,
    keywords: [
      'api',
      'integration',
      'web',
      'hook',
      'connect',
      'link',
      'push api',
      'callback',
    ],
  },
  {
    name: 'rabbit',
    icon: RabbitIcon,
    keywords: [
      'bunny',
      'hare',
      'animal',
      'mammal',
      'ears',
      'fluffy',
      'rodent',
      'pet',
      'pest',
      'bunny',
      'hare',
      'fast',
      'speed',
      'hop',
    ],
  },
  {
    name: 'cog',
    icon: CogIcon,
    keywords: [
      'gear',
      'settings',
      'preferences',
      'controls',
      'computing',
      'settings',
      'cog',
      'edit',
    ],
  },
  {
    name: 'cpu',
    icon: CpuIcon,
    keywords: [
      'processor',
      'chip',
      'computer',
      'hardware',
      'central processing unit',
      'computing',
      'cores',
      'technology',
      'circuit',
      'memory',
      'ram',
      'specs',
      'gigahertz',
      'ghz',
    ],
  },
  {
    name: 'sparkles',
    icon: SparklesIcon,
    keywords: ['sparkles', 'sparkle', 'star', 'stars', 'shine', 'shining'],
  },
  {
    name: 'rocket',
    icon: RocketIcon,
    keywords: ['launch', 'space', 'boost', 'release', 'version'],
  },
  {
    name: 'activity',
    icon: ActivityIcon,
    keywords: [
      'pulse',
      'action',
      'motion/react',
      'movement',
      'exercise',
      'fitness',
      'healthcare',
      'heart rate monitor',
      'vital signs',
      'vitals',
      'emergency room',
      'er',
      'intensive care',
      'hospital',
      'defibrillator',
      'earthquake',
      'siesmic',
      'magnitude',
      'richter scale',
      'aftershock',
      'tremor',
      'shockwave',
      'audio',
      'waveform',
      'synthesizer',
      'synthesiser',
      'music',
    ],
  },
  {
    name: 'ban',
    icon: BanIcon,
    keywords: ['ban', 'stop', 'prevent', 'no'],
  },
  {
    name: 'map-pin',
    icon: MapPinIcon,
    keywords: ['map', 'pin', 'marker', 'location', 'address'],
  },
  {
    name: 'map-pin-check-inside',
    icon: MapPinCheckInsideIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'done',
      'tick',
      'complete',
      'task',
      'added',
    ],
  },
  {
    name: 'map-pin-minus-inside',
    icon: MapPinMinusInsideIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'delete',
      'remove',
      'erase',
    ],
  },
  {
    name: 'map-pin-plus-inside',
    icon: MapPinPlusInsideIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'add',
      'create',
      'new',
    ],
  },
  {
    name: 'map-pin-off',
    icon: MapPinOffIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'off',
      'remove',
      'non available',
      'invalid',
    ],
  },
  {
    name: 'map-pin-check',
    icon: MapPinCheckIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'done',
      'tick',
      'complete',
      'task',
      'added',
    ],
  },
  {
    name: 'map-pin-house',
    icon: MapPinHouseIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'home',
      'living',
      'place',
      'landmark',
    ],
  },
  {
    name: 'map-pin-minus',
    icon: MapPinMinusIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'delete',
      'remove',
      'erase',
    ],
  },
  {
    name: 'map-pin-plus',
    icon: MapPinPlusIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'add',
      'create',
      'new',
    ],
  },
  {
    name: 'map-pin-x-inside',
    icon: MapPinXInsideIcon,
    keywords: [
      'map',
      'pin',
      'marker',
      'location',
      'address',
      'waypoint',
      'delete',
      'remove',
      'erase',
    ],
  },
  {
    name: 'battery-full',
    icon: BatteryFullIcon,
    keywords: ['battery', 'power', 'energy', 'full', 'charge'],
  },
  {
    name: 'terminal',
    icon: TerminalIcon,
    keywords: ['terminal', 'console', 'command line', 'shell', 'prompt'],
  },
  {
    name: 'keyboard',
    icon: KeyboardIcon,
    keywords: ['keyboard', 'input', 'text', 'type', 'input method'],
  },
  {
    name: 'clap',
    icon: ClapIcon,
    keywords: [
      'clap',
      'movie',
      'film',
      'video',
      'camera',
      'cinema',
      'cut',
      'action',
      'television',
      'tv',
      'show',
      'entertainment',
    ],
  },
  {
    name: 'layout-panel-top',
    icon: LayoutPanelTopIcon,
    keywords: [
      'window',
      'webpage',
      'block',
      'section',
      'grid',
      'template',
      'structure',
    ],
  },
  {
    name: 'book-text',
    icon: BookTextIcon,
    keywords: [
      'reading',
      'booklet',
      'magazine',
      'leaflet',
      'pamphlet',
      'tome',
      'library',
    ],
  },
  {
    name: 'shower-head',
    icon: ShowerHeadIcon,
    keywords: ['shower', 'bath', 'bathroom', 'amenities', 'services'],
  },
  {
    name: 'telescope',
    icon: TelescopeIcon,
    keywords: [
      'telescope',
      'astronomy',
      'space',
      'discovery',
      'exploration',
      'explore',
      'vision',
      'perspective',
      'focus',
      'stargazing',
      'observe',
      'view',
    ],
  },
  {
    name: 'wind',
    icon: WindIcon,
    keywords: ['wind', 'weather', 'air', 'blow'],
  },
  {
    name: 'angry',
    icon: AngryIcon,
    keywords: ['mad', 'upset', 'angry', 'furious', 'emotion', 'face'],
  },
  {
    name: 'cctv',
    icon: CctvIcon,
    keywords: [
      'cctv',
      'camera',
      'surveillance',
      'recording',
      'film',
      'videotape',
      'crime',
      'watching',
    ],
  },
  {
    name: 'coffee',
    icon: CoffeeIcon,
    keywords: [
      'coffee',
      'drink',
      'cup',
      'mug',
      'tea',
      'cafe',
      'hot',
      'beverage',
    ],
  },
  {
    name: 'arrow-down-a-z',
    icon: ArrowDownAZIcon,
    keywords: [
      'filter',
      'sort',
      'ascending',
      'descending',
      'increasing',
      'decreasing',
      'rising',
      'falling',
      'alphabetical',
    ],
  },
  {
    name: 'arrow-down-z-a',
    icon: ArrowDownZAIcon,
    keywords: [
      'filter',
      'sort',
      'ascending',
      'descending',
      'increasing',
      'decreasing',
      'rising',
      'falling',
      'alphabetical',
    ],
  },
  {
    name: 'arrow-down-0-1',
    icon: ArrowDown01con,
    keywords: [
      'filter',
      'sort',
      'ascending',
      'descending',
      'increasing',
      'decreasing',
      'rising',
      'falling',
      'numerical',
    ],
  },
  {
    name: 'arrow-down-1-0',
    icon: ArrowDown10Icon,
    keywords: [
      'filter',
      'sort',
      'ascending',
      'descending',
      'increasing',
      'decreasing',
      'rising',
      'falling',
      'numerical',
    ],
  },
  {
    name: 'x',
    icon: XIcon,
    keywords: ['x', 'close', 'delete', 'remove', 'cancel', 'exit', 'stop'],
  },
  {
    name: 'cast',
    icon: CastIcon,
    keywords: ['cast', 'screen', 'chromecast', 'airplay'],
  },
  {
    name: 'upload',
    icon: UploadIcon,
    keywords: ['upload', 'send', 'share'],
  },
  {
    name: 'file-cog',
    icon: FileCogIcon,
    keywords: ['file', 'cog', 'settings', 'preferences', 'controls'],
  },
  {
    name: 'calendar-days',
    icon: CalendarDaysIcon,
    keywords: ['calendar', 'days', 'days of the week', 'week', 'month'],
  },
  {
    name: 'chart-line',
    icon: ChartLineIcon,
    keywords: ['chart', 'line', 'increasing', 'linechart', 'chartline'],
  },
  {
    name: 'file-chart-line',
    icon: FileChartLineIcon,
    keywords: [
      'file',
      'chart',
      'line',
      'increasing',
      'filechartline',
      'filechartline',
    ],
  },
  {
    name: 'chart-no-axes-column-increasing',
    icon: ChartNoAxesColumnIncreasingIcon,
    keywords: ['chart', 'column', 'increasing', 'chartnoaxes'],
  },
  {
    name: 'chart-no-axes-column-decreasing',
    icon: ChartNoAxesColumnDecreasingIcon,
    keywords: ['chart', 'column', 'decreasing', 'chartnoaxes'],
  },
  {
    name: 'radio',
    icon: RadioIcon,
    keywords: [
      'radio',
      'signal',
      'broadcast',
      'wireless',
      'frequency',
      'connectivity',
      'live',
    ],
  },
  {
    name: 'radio-tower',
    icon: RadioTowerIcon,
    keywords: [
      'radio',
      'tower',
      'radio tower',
      'signal',
      'broadcast',
      'wireless',
      'frequency',
      'connectivity',
      'live',
    ],
  },
  {
    name: 'blocks',
    icon: BlocksIcon,
    keywords: [
      'block',
      'blocks',
      'addon',
      'plugin',
      'integration',
      'extension',
      'package',
      'build',
      'stack',
      'toys',
      'kids',
      'children',
      'learning',
    ],
  },
  {
    name: 'calendar-check',
    icon: CalendarCheckIcon,
    keywords: [
      'calendar',
      'check',
      'checkmark',
      'tick',
      'done',
      'confirm',
      'complete',
    ],
  },
  {
    name: 'calendar-check-2',
    icon: CalendarCheck2Icon,
    keywords: [
      'calendar',
      'check',
      'checkmark',
      'tick',
      'done',
      'confirm',
      'complete',
    ],
  },
  {
    name: 'file-check',
    icon: FileCheckIcon,
    keywords: [
      'file',
      'check',
      'checkmark',
      'tick',
      'done',
      'document',
      'confirm',
      'complete',
    ],
  },
  {
    name: 'file-check-2',
    icon: FileCheck2Icon,
    keywords: [
      'file',
      'check',
      'checkmark',
      'tick',
      'done',
      'document',
      'confirm',
      'complete',
    ],
  },
  {
    name: 'mail-check',
    icon: MailCheckIcon,
    keywords: [
      'mail',
      'check',
      'checkmark',
      'tick',
      'done',
      'email',
      'confirm',
      'complete',
      'delivered',
      'message',
      'sent',
    ],
  },
  {
    name: 'monitor-check',
    icon: MonitorCheckIcon,
    keywords: [
      'monitor',
      'check',
      'checkmark',
      'tick',
      'done',
      'confirm',
      'complete',
      'tv',
      'screen',
      'display',
      'desktop',
      'running',
      'active',
      'virtual machine',
      'vm',
    ],
  },
  {
    name: 'laptop-minimal-check',
    icon: LaptopMinimalCheckIcon,
    keywords: [
      'laptop',
      'check',
      'checkmark',
      'tick',
      'done',
      'confirm',
      'complete',
      'computer',
      'laptop',
      'running',
      'active',
      'virtual machine',
      'vm',
    ],
  },
  {
    name: 'gallery-horizontal-end',
    icon: GalleryHorizontalEndIcon,
    keywords: [
      'gallery',
      'horizontal',
      'end',
      'last',
      'carousel',
      'pictures',
      'images',
      'scroll',
      'swipe',
      'album',
      'portfolio',
      'history',
      'versions',
      'backup',
      'time machine',
    ],
  },
  {
    name: 'gallery-vertical-end',
    icon: GalleryVerticalEndIcon,
    keywords: [
      'carousel',
      'pictures',
      'images',
      'scroll',
      'swipe',
      'album',
      'portfolio',
      'history',
      'versions',
      'backup',
      'time machine',
    ],
  },
  {
    name: 'hand-heart',
    icon: HandHeartIcon,
    keywords: [
      'hand',
      'heart',
      'love',
      'affection',
      'hug',
      'cuddle',
      'emotion',
    ],
  },
  {
    name: 'gallery-thumbnails',
    icon: GalleryThumbnailsIcon,
    keywords: [
      'gallery',
      'thumbnails',
      'images',
      'carousel',
      'pictures',
      'album',
      'portfolio',
      'preview',
    ],
  },
  {
    name: 'user-check',
    icon: UserCheckIcon,
    keywords: [
      'user',
      'check',
      'checkmark',
      'tick',
      'done',
      'confirm',
      'complete',
    ],
  },
  {
    name: 'user-round-check',
    icon: UserRoundCheckIcon,
    keywords: [
      'user',
      'check',
      'checkmark',
      'tick',
      'done',
      'confirm',
      'complete',
    ],
  },
  {
    name: 'boxes',
    icon: BoxesIcon,
    keywords: ['boxes', 'box', 'container', 'package', 'shipping'],
  },
  {
    name: 'file-text',
    icon: FileTextIcon,
    keywords: ['file', 'text', 'document', 'document', 'document'],
  },
  {
    name: 'user-round-plus',
    icon: UserRoundPlusIcon,
    keywords: ['user', 'plus', 'add', 'create', 'new'],
  },
  {
    name: 'panel-left-open',
    icon: PanelLeftOpenIcon,
    keywords: ['panel', 'left', 'open', 'menu', 'navigation'],
  },
  {
    name: 'panel-left-close',
    icon: PanelLeftCloseIcon,
    keywords: ['panel', 'left', 'close', 'menu', 'navigation'],
  },
  {
    name: 'panel-right-open',
    icon: PanelRightOpenIcon,
    keywords: ['panel', 'right', 'open', 'menu', 'navigation'],
  },
];

export {
  AlarmClockIcon,
  AlignCenterIcon,
  AlignHorizontalIcon,
  AlignVerticalIcon,
  AngryIcon,
  ArchiveIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  AtSignIcon,
  AttachFileIcon,
  BadgePercentIcon,
  BellIcon,
  BoldIcon,
  BoneIcon,
  CalendarCogIcon,
  ChartPieIcon,
  ChartScatterIcon,
  CircleCheckIcon,
  CircleDollarSignIcon,
  ClockIcon,
  CopyIcon,
  CursorClickIcon,
  DeleteIcon,
  DownloadIcon,
  DownvoteIcon,
  SquarePenIcon,
  ExpandIcon,
  FilePenLineIcon,
  FileStackIcon,
  FingerprintIcon,
  FrameIcon,
  GaugeIcon,
  GitPullRequestIcon,
  GithubIcon,
  GripIcon,
  HandCoinsIcon,
  HomeIcon,
  ItalicIcon,
  LanguagesIcon,
  LayersIcon,
  LinkIcon,
  MenuIcon,
  PartyPopperIcon,
  PenToolIcon,
  RefreshCCWIcon,
  RouteIcon,
  ScanTextIcon,
  SettingsIcon,
  SettingsGearIcon,
  SunIcon,
  UnderlineIcon,
  UndoIcon,
  ConnectIcon,
  UpvoteIcon,
  UsersIcon,
  VolumeIcon,
  CartIcon,
  StethoscopeIcon,
  EarthIcon,
  WorkflowIcon,
  LogoutIcon,
  CircleHelpIcon,
  UserIcon,
  AudioLinesIcon,
  FlameIcon,
  EyeOffIcon,
  SquareStackIcon,
  BadgeAlertIcon,
  MessageCircleIcon,
  MessageCircleMoreIcon,
  SearchIcon,
  ShieldCheckIcon,
  TimerIcon,
  BluetoothSearchingIcon,
  BluetoothConnectedIcon,
  BluetoothOffIcon,
  FlaskIcon,
  SyringeIcon,
  AArrowDownIcon,
  CompassIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  TrendingUpDownIcon,
  PlayIcon,
  PauseIcon,
  ChevronsUpDownIcon,
  ChevronsDownUpIcon,
  ChevronsLeftRightIcon,
  ChevronsRightLeftIcon,
  CircleChevronDownIcon,
  CircleChevronLeftIcon,
  CircleChevronRightIcon,
  CircleChevronUpIcon,
  CircleDashedIcon,
  CheckIcon,
  CheckCheckIcon,
  IdCardIcon,
  LoaderPinwheelIcon,
  RockingChairIcon,
  ChartColumnDecreasingIcon,
  ChartColumnIncreasingIcon,
  ChartBarDecreasingIcon,
  ChartBarIncreasingIcon,
  BananaIcon,
  BanIcon,
  WifiIcon,
  ChromeIcon,
  FigmaIcon,
  FishSymbolIcon,
  GitCommitVerticalIcon,
  GitCommitHorizontalIcon,
  WaypointsIcon,
  ShipIcon,
  RollerCoasterIcon,
  AirplaneIcon,
  DrumIcon,
  TrainTrackIcon,
  SparklesIcon,
  WebhookIcon,
  RabbitIcon,
  CogIcon,
  CpuIcon,
  RocketIcon,
  ActivityIcon,
  MapPinIcon,
  MapPinCheckInsideIcon,
  MapPinMinusInsideIcon,
  MapPinPlusInsideIcon,
  MapPinOffIcon,
  MapPinCheckIcon,
  MapPinHouseIcon,
  MapPinMinusIcon,
  MapPinPlusIcon,
  MapPinXInsideIcon,
  BatteryFullIcon,
  TerminalIcon,
  KeyboardIcon,
  ClapIcon,
  LayoutPanelTopIcon,
  BookTextIcon,
  ShowerHeadIcon,
  TelescopeIcon,
  WindIcon,
  CctvIcon,
  CoffeeIcon,
  ArrowDownZAIcon,
  ArrowDownAZIcon,
  ArrowDown01con,
  ArrowDown10Icon,
  ClipboardCheckIcon,
  FacebookIcon,
  LinkedinIcon,
  TwitterIcon,
  YoutubeIcon,
  InstagramIcon,
  TwitchIcon,
  DribbbleIcon,
  DiscordIcon,
  XIcon,
  MoonIcon,
  VibrateIcon,
  SmartphoneChargingIcon,
  CastIcon,
  UploadIcon,
  CloudSunIcon,
  SunsetIcon,
  SunDimIcon,
  SunMediumIcon,
  SunMoonIcon,
  MessageSquareIcon,
  MessageSquareMoreIcon,
  MessageCircleDashedIcon,
  MessageSquareDashedIcon,
  AArrowUpIcon,
  FileCogIcon,
  CalendarDaysIcon,
  ArrowBigDownDashIcon,
  ArrowBigLeftDashIcon,
  ArrowBigRightDashIcon,
  ArrowBigUpDashIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowBigDownIcon,
  ArrowBigLeftIcon,
  ArrowBigRightIcon,
  ArrowBigUpIcon,
  KeyCircleIcon,
  KeySquareIcon,
  ChartLineIcon,
  FileChartLineIcon,
  ChartNoAxesColumnIncreasingIcon,
  ChartNoAxesColumnDecreasingIcon,
  RadioIcon,
  RadioTowerIcon,
  AirVentIcon,
  TornadoIcon,
  WindArrowDownIcon,
  CloudRainIcon,
  CloudRainWindIcon,
  WavesIcon,
  WavesLadderIcon,
  SquareArrowDownIcon,
  SquareArrowLeftIcon,
  SquareArrowUpIcon,
  SquareArrowRightIcon,
  BlocksIcon,
  CalendarCheckIcon,
  CalendarCheck2Icon,
  FileCheckIcon,
  FileCheck2Icon,
  MailCheckIcon,
  MonitorCheckIcon,
  LaptopMinimalCheckIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ChevronLeftIcon,
  SquareChevronDownIcon,
  SquareChevronUpIcon,
  SquareChevronRightIcon,
  SquareChevronLeftIcon,
  GalleryHorizontalEndIcon,
  GalleryVerticalEndIcon,
  HandHeartIcon,
  SquareActivityIcon,
  RotateCWIcon,
  RotateCCWIcon,
  GalleryThumbnailsIcon,
  UserCheckIcon,
  UserRoundCheckIcon,
  BoxesIcon,
  RefreshCWIcon,
  RefreshCCWOffIcon,
  RedoDotIcon,
  ScanFaceIcon,
  FrownIcon,
  SmilePlusIcon,
  SmileIcon,
  LaughIcon,
  AnnoyedIcon,
  FileTextIcon,
  UserRoundPlusIcon,
  PanelLeftOpenIcon,
  PanelLeftCloseIcon,
  PanelRightOpenIcon,
  HeartIcon,
  PlusIcon,
  ICON_LIST,
};
