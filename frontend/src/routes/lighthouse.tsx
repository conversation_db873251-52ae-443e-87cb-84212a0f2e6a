import { createFileRoute } from '@tanstack/react-router'
import { LighthouseMain } from '~/components/lighthouse'
import { motion } from 'framer-motion'
import { z } from 'zod'

const lighthouseSearchSchema = z.object({
  module: z.enum(['dashboard', 'knowledge', 'research', 'agents', 'chat', 'sources', 'analytics', 'insights']).optional(),
})

export const Route = createFileRoute('/lighthouse')({
  component: LighthouseComponent,
  validateSearch: lighthouseSearchSchema,
})

function LighthouseComponent() {
  const { module } = Route.useSearch()
  return (
    <div className="h-screen overflow-hidden bg-gradient-to-br from-slate-50 to-blue-50/30 dark:from-gray-950 dark:to-blue-950/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05] pointer-events-none">
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_35%,rgba(59,130,246,0.1)_35%,rgba(59,130,246,0.1)_65%,transparent_65%),linear-gradient(-45deg,transparent_35%,rgba(147,51,234,0.1)_35%,rgba(147,51,234,0.1)_65%,transparent_65%)] bg-[length:60px_60px]" />
      </div>
      
      {/* Main Lighthouse Interface */}
      <motion.div
        initial={{ opacity: 0, scale: 0.98 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative z-10 h-full"
      >
        <LighthouseMain 
          initialModule={module || "dashboard"}
          className="h-full"
        />
      </motion.div>
      
      {/* Ambient glow effects */}
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl pointer-events-none" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl pointer-events-none" />
    </div>
  )
}